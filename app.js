require('dotenv').config(); // 加载环境变量
const express = require('express');
const cors = require('cors');
const path = require('path');
const axios = require('axios');

// 创建Express应用
const app = express();

// 启用CORS并配置
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type']
}));

// 启用 JSON 解析
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 设置静态文件目录
app.use('/static', express.static(path.join(__dirname, 'static'), {
    maxAge: '1y',
    etag: true
}));

// 设置js目录的静态文件服务
app.use('/src/js', express.static(path.join(__dirname, 'src/js'), {
    maxAge: '1y',
    etag: true
}));

// 设置css目录的静态文件服务
app.use('/src/css', express.static(path.join(__dirname, 'src/css'), {
    maxAge: '1y',
    etag: true
}));

// 设置node_modules目录路由，特别是Cesium资源
app.use('/node_modules', express.static(path.join(__dirname, 'node_modules'), {
    maxAge: '1y',
    etag: true
}));

// 设置src目录的静态文件服务
app.use('/src', express.static(path.join(__dirname, 'src'), {
    maxAge: '1y',
    etag: true
}));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 健康检查路由
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 设置端口
const PORT = process.env.PORT || 8888;
const HOST = process.env.HOST || '0.0.0.0';

// 启动服务器
app.listen(PORT, HOST, () => {
    console.log(`服务器启动成功！`);
    console.log(`请在浏览器中访问: http://localhost:${PORT}`);
});

// 捕获未处理的异常
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
});

// 捕获未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});
