// 位置搜索类
class LocationSearch {
    constructor(viewer) {
        this.viewer = viewer;
        this.searchMarker = null;
    }

    // 解析坐标字符串
    parseCoordinates(input) {
        // 移除所有空格
        input = input.replace(/\s+/g, '');
        
        // 尝试分割坐标（支持逗号、中文顿号、空格分隔）
        const parts = input.split(/[,，。.]/);
        if (parts.length !== 2) {
            return null;
        }

        const [lon, lat] = parts;
        
        // 检查是否是度分秒格式
        if (lon.includes('°') || lat.includes('°')) {
            return this.parseDMS(lon, lat);
        }

        // 尝试解析为十进制度
        const longitude = parseFloat(lon);
        const latitude = parseFloat(lat);

        if (isNaN(longitude) || isNaN(latitude)) {
            return null;
        }

        // 验证坐标范围
        if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
            return null;
        }

        return { longitude, latitude };
    }

    // 解析度分秒格式
    parseDMS(lonStr, latStr) {
        const parseDMSPart = (str) => {
            // 移除所有空格
            str = str.replace(/\s+/g, '');
            
            // 匹配度分秒格式
            const match = str.match(/^(\d+)°(?:(\d+)')?(?:(\d+(?:\.\d+)?)")?([NSEW])?$/);
            if (!match) return null;

            let [_, degrees, minutes = '0', seconds = '0', direction] = match;
            
            let decimal = parseFloat(degrees) +
                         parseFloat(minutes) / 60 +
                         parseFloat(seconds) / 3600;

            if (direction === 'S' || direction === 'W') {
                decimal = -decimal;
            }

            return decimal;
        };

        const lon = parseDMSPart(lonStr);
        const lat = parseDMSPart(latStr);

        if (lon === null || lat === null) {
            return null;
        }

        return { longitude: lon, latitude: lat };
    }

    // 搜索地点
    async searchLocation(searchText) {
        console.log('LocationSearch.searchLocation 被调用，搜索文本:', searchText);
        
        // 首先尝试解析为坐标
        const coordinates = this.parseCoordinates(searchText);
        console.log('坐标解析结果:', coordinates);
        
        if (coordinates) {
            console.log('检测到坐标格式，准备飞行到位置');
            this.flyToLocation(coordinates.longitude, coordinates.latitude);
            return;
        }

        // 如果不是坐标，则尝试地名搜索
        console.log('未检测到坐标格式，尝试地名搜索');
        try {
            // 使用本地模拟数据代替在线API，避免跨域和连接问题
            console.log('使用本地模拟数据代替在线API请求');
            
            // 模拟数据
            const mockData = [
                { 
                    lon: 117.1200, 
                    lat: 36.6512, 
                    display_name: '济南市, 山东省, 中国',
                    importance: 0.9
                }
            ];
            
            // 模拟搜索逻辑
            let data = [];
            if (searchText.includes('济南')) {
                data = mockData;
            }
            console.log('搜索响应数据:', data);

            if (data && data.length > 0) {
                const location = data[0];
                console.log('找到位置:', location);
                this.flyToLocation(parseFloat(location.lon), parseFloat(location.lat));
            } else {
                console.log('未找到位置');
                alert('未找到该地点，请检查输入是否正确。');
            }
        } catch (error) {
            console.error('搜索地点时出错：', error);
            alert('搜索失败，请稍后重试。');
        }
    }

    // 飞行到指定位置
    flyToLocation(longitude, latitude) {
        console.log('flyToLocation 被调用:', { longitude, latitude });
        
        // 移除之前的标记
        if (this.searchMarker) {
            console.log('移除现有标记');
            this.viewer.entities.remove(this.searchMarker);
        }

        // 添加新的标记
        try {
            console.log('正在添加新标记');
            this.searchMarker = this.viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
                billboard: {
                    image: this.getSearchMarkerIcon(),
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    scale: 1.0,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                },
                label: {
                    text: `${longitude.toFixed(6)}, ${latitude.toFixed(6)}`,
                    font: '14px sans-serif',
                    fillColor: Cesium.Color.WHITE,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.TOP,
                    pixelOffset: new Cesium.Cartesian2(0, 5),
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                    backgroundColor: Cesium.Color.fromCssColorString('rgba(0,0,0,0.7)'),
                    showBackground: true,
                    backgroundPadding: new Cesium.Cartesian2(7, 5)
                }
            });
            console.log('新标记添加成功');
        } catch (error) {
            console.error('添加标记时出错:', error);
        }

        // 飞行到位置
        try {
            console.log('开始飞行到目标位置');
            this.viewer.camera.flyTo({
                destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 10000.0),
                duration: 2
            });
        } catch (error) {
            console.error('飞行到位置时出错:', error);
        }
    }

    // 获取搜索标记图标
    getSearchMarkerIcon() {
        const svgIcon = `data:image/svg+xml;base64,${btoa(`<?xml version="1.0" encoding="UTF-8"?>
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="16" cy="16" r="14" fill="rgba(255,255,255,0.3)"/>
                <path d="M16 2C10.48 2 6 6.48 6 12C6 19.5 16 30 16 30C16 30 26 19.5 26 12C26 6.48 21.52 2 16 2ZM16 15C14.34 15 13 13.66 13 12C13 10.34 14.34 9 16 9C17.66 9 19 10.34 19 12C19 13.66 17.66 15 16 15Z" 
                      fill="#FF4444" 
                      stroke="white" 
                      stroke-width="1.5"/>
            </svg>`)}`;
        return svgIcon;
    }

    // 清除搜索标记
    clearSearchMarker() {
        console.log('clearSearchMarker 被调用');
        if (this.searchMarker) {
            console.log('正在移除搜索标记');
            this.viewer.entities.remove(this.searchMarker);
            this.searchMarker = null;
            console.log('搜索标记已清除');
        } else {
            console.log('没有搜索标记需要清除');
        }
    }

    // 添加显示面板方法
    showPanel(button) {
        console.log('LocationSearch.showPanel 被调用');
        const panel = document.getElementById('searchPanel');
        console.log(`搜索面板元素: ${panel ? '已找到' : '未找到'}`);
        
        if (panel) {
            // 确保面板可见性设置正确
            panel.style.display = 'block';
            console.log('搜索面板显示属性已设置为block');
            
            // 检查PanelPositioner是否可用
            if (typeof PanelPositioner === 'undefined') {
                console.error('PanelPositioner未定义，无法定位面板');
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
                console.log('已使用默认位置');
            } else {
                console.log('正在使用PanelPositioner设置面板位置');
                try {
                    // 使用PanelPositioner设置面板位置 - 改为右侧显示，避免遮挡下方按钮
                    PanelPositioner.setPosition(button, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                    console.log('面板位置设置完成');
                    
                    // 获取面板当前位置
                    const panelRect = panel.getBoundingClientRect();
                    const viewportHeight = window.innerHeight;
                    
                    // 调整垂直位置，确保不会超出屏幕下方或遮挡底部按钮
                    if (panelRect.bottom > viewportHeight - 60) { // 保留底部60px空间给按钮
                        const newTop = Math.max(10, viewportHeight - 60 - panelRect.height);
                        panel.style.top = `${newTop}px`;
                        console.log(`调整面板垂直位置到 ${newTop}px，避免遮挡底部按钮`);
                    }
                } catch (error) {
                    console.error('设置面板位置时出错:', error);
                    // 默认位置
                    panel.style.position = 'fixed';
                    panel.style.top = '100px';
                    panel.style.left = '100px';
                    console.log('出错后已使用默认位置');
                }
            }
            
            // 强制显示检查
            setTimeout(() => {
                if (panel.style.display !== 'block') {
                    console.warn('延迟检查: 面板仍未显示，强制设置为显示');
                    panel.style.display = 'block';
                }
                // 触发布局重新计算
                panel.offsetHeight;
            }, 100);
            
            // 聚焦搜索输入框
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                setTimeout(() => {
                    searchInput.focus();
                    console.log('已聚焦搜索输入框');
                }, 200);
            }
        } else {
            console.error('未找到搜索面板元素，无法显示');
        }
    }

    // 添加隐藏面板方法
    hidePanel() {
        console.log('LocationSearch.hidePanel 被调用');
        const panel = document.getElementById('searchPanel');
        if (panel) {
            panel.style.display = 'none';
            console.log('搜索面板已隐藏');
        } else {
            console.error('未找到搜索面板元素，无法隐藏');
        }
    }
}

// 添加静态初始化方法
LocationSearch.init = function(viewer, toggleBtnId = 'toggleSearch') {
    console.log(`LocationSearch.init 开始初始化, 按钮ID: ${toggleBtnId}`);
    const search = new LocationSearch(viewer);
    
    // 获取切换按钮
    const toggleBtn = document.getElementById(toggleBtnId);
    console.log(`搜索切换按钮元素: ${toggleBtn ? '已找到' : '未找到'}`);
    
    if (toggleBtn) {
        // 清除现有事件监听器，避免重复绑定
        const newToggleBtn = toggleBtn.cloneNode(true);
        if (toggleBtn.parentNode) {
            toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
            console.log('已清除并替换搜索按钮元素，避免事件重复绑定');
        }
        
        // 重新绑定事件监听器
        newToggleBtn.addEventListener('click', function(e) {
            console.log('搜索面板切换按钮被点击 (新绑定的事件监听器)');
            const panel = document.getElementById('searchPanel');
            console.log(`搜索面板元素: ${panel ? '已找到' : '未找到'}, 当前显示状态: ${panel ? panel.style.display : 'N/A'}`);
            
            if (panel) {
                if (panel.style.display === 'none' || !panel.style.display) {
                    // 隐藏其他面板
                    const otherPanels = ['terrainDigPanel', 'measureToolContainer', 'profileAnalysisPanel'];
                    otherPanels.forEach(panelId => {
                        const otherPanel = document.getElementById(panelId);
                        if (otherPanel) {
                            otherPanel.style.display = 'none';
                            console.log(`已隐藏面板: ${panelId}`);
                        }
                    });
                    
                    // 显示搜索面板
                    console.log('正在显示搜索面板...');
                    search.showPanel(newToggleBtn);
                } else {
                    console.log('正在隐藏搜索面板...');
                    search.hidePanel();
                }
            } else {
                console.error('未找到搜索面板元素!');
            }
        });
        console.log('搜索按钮事件绑定完成');
    } else {
        console.error(`未找到搜索按钮元素! ID: ${toggleBtnId}`);
    }

    // 获取搜索和清除按钮
    const searchButton = document.getElementById('searchButton');
    const clearButton = document.getElementById('clearButton');
    const searchInput = document.getElementById('searchInput');
    console.log(`搜索按钮元素: ${searchButton ? '已找到' : '未找到'}`);
    console.log(`清除按钮元素: ${clearButton ? '已找到' : '未找到'}`);
    console.log(`搜索输入框元素: ${searchInput ? '已找到' : '未找到'}`);

    // 添加搜索按钮点击事件
    if (searchButton) {
        // 清除现有事件监听器
        const newSearchButton = searchButton.cloneNode(true);
        if (searchButton.parentNode) {
            searchButton.parentNode.replaceChild(newSearchButton, searchButton);
        }
        
        newSearchButton.addEventListener('click', function() {
            console.log('搜索按钮被点击 (新绑定的事件监听器)');
            if (searchInput) {
                const searchText = searchInput.value.trim();
                console.log('搜索文本:', searchText);
                search.searchLocation(searchText);
            } else {
                console.error('未找到搜索输入框元素!');
            }
        });
    }

    // 添加清除按钮点击事件
    if (clearButton) {
        // 清除现有事件监听器
        const newClearButton = clearButton.cloneNode(true);
        if (clearButton.parentNode) {
            clearButton.parentNode.replaceChild(newClearButton, clearButton);
        }
        
        newClearButton.addEventListener('click', function() {
            console.log('清除按钮被点击 (新绑定的事件监听器)');
            if (searchInput) {
                searchInput.value = '';
                console.log('输入框已清空');
            } else {
                console.error('未找到搜索输入框元素!');
            }
            search.clearSearchMarker();
        });
    }

    // 添加输入框回车事件
    if (searchInput) {
        // 清除现有事件监听器
        const newSearchInput = searchInput.cloneNode(true);
        if (searchInput.parentNode) {
            searchInput.parentNode.replaceChild(newSearchInput, searchInput);
        }
        
        newSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                console.log('搜索输入框回车 (新绑定的事件监听器)');
                const searchText = this.value.trim();
                console.log('搜索文本:', searchText);
                search.searchLocation(searchText);
            }
        });
    }
    
    console.log('LocationSearch.init 初始化完成');
    return search;
};

// 导出类
window.LocationSearch = LocationSearch; 