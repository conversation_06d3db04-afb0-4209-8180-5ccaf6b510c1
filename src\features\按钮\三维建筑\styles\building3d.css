/* 3D建筑功能样式 */

/* 3D建筑面板样式 */
#building3DPanel {
    position: fixed;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(200, 200, 200, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
    padding: 20px;
    min-width: 300px;
    max-width: 350px;
}

/* 面板标题样式 */
#building3DPanel .panel-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    color: #333;
}

/* 建筑控件容器 */
.building-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 建筑类型选择器 */
.building-type-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.building-type-selector label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.building-type-selector select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.building-type-selector select:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
}

/* 建筑高度输入 */
.building-height-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.building-height-input label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
    min-width: 80px;
}

.building-height-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.building-height-input input:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
}

.building-height-input span {
    font-size: 14px;
    color: #666;
    min-width: 20px;
}

/* 建筑颜色输入 */
.building-color-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.building-color-input label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
    min-width: 80px;
}

.building-color-input input[type="color"] {
    flex: 1;
    height: 40px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    outline: none;
    transition: border-color 0.3s ease;
}

.building-color-input input[type="color"]:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
}

/* 按钮组样式 */
#building3DPanel .button-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 20px;
}

/* 建筑按钮样式 */
.building-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    outline: none;
}

.building-btn.primary {
    background-color: #4A90E2;
    color: white;
}

.building-btn.primary:hover {
    background-color: #357ABD;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(74, 144, 226, 0.3);
}

.building-btn.primary.active {
    background-color: #7ED321;
    animation: pulse 2s infinite;
}

.building-btn.danger {
    background-color: #FF6B6B;
    color: white;
}

.building-btn.danger:hover {
    background-color: #FF5252;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(255, 107, 107, 0.3);
}

/* 建筑信息提示 */
.building-info {
    margin-top: 15px;
    padding: 10px;
    background-color: rgba(74, 144, 226, 0.1);
    border-left: 3px solid #4A90E2;
    border-radius: 4px;
}

.building-info p {
    margin: 0;
    font-size: 13px;
    color: #555;
    line-height: 1.4;
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(126, 211, 33, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(126, 211, 33, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(126, 211, 33, 0);
    }
}

/* 工具按钮样式 - 确保与现有按钮样式一致 */
#toggleBuilding3D {
    width: 48px;
    height: 48px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    position: relative;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#toggleBuilding3D img {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

#toggleBuilding3D:hover {
    background-color: #2196F3;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#toggleBuilding3D:hover img {
    filter: brightness(0) invert(1);
    transform: scale(1.1);
}

#toggleBuilding3D .tooltip {
    position: absolute;
    right: 60px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    pointer-events: none;
}

#toggleBuilding3D:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #building3DPanel {
        min-width: 280px;
        max-width: 90vw;
        padding: 15px;
    }
    
    .building-height-input,
    .building-color-input {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .building-height-input label,
    .building-color-input label {
        min-width: auto;
    }
    
    #building3DPanel .button-group {
        grid-template-columns: 1fr;
    }
}

/* 面板显示动画 */
#building3DPanel {
    animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
} 