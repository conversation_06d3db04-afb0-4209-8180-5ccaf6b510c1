/**
 * 按钮配置系统 - 集中管理所有工具按钮的配置信息
 * 解决配置分散、路径硬编码、维护困难的问题
 */
class ButtonConfig {
    constructor() {
        // 基础路径配置
        this.basePaths = {
            buttons: 'src/features/按钮',
            layout: 'src/features/布局',
            utils: 'src/features/工具类'
        };

        // 按钮分类定义
        this.categories = {
            measurement: { name: '测量分析', color: '#4CAF50', order: 1 },
            terrain: { name: '地形分析', color: '#FF9800', order: 2 },
            navigation: { name: '导航定位', color: '#2196F3', order: 3 },
            management: { name: '数据管理', color: '#9C27B0', order: 4 },
            visualization: { name: '可视化', color: '#F44336', order: 5 },
            tools: { name: '工具集', color: '#607D8B', order: 6 }
        };

        // 核心按钮配置
        this.configs = this._initializeConfigs();
        
        console.log('✓ ButtonConfig 配置系统初始化完成');
    }

    /**
     * 初始化所有按钮配置
     */
    _initializeConfigs() {
        return {
            // 测量工具
            measure: {
                id: 'measure',
                name: '测量工具',
                tooltip: '距离、面积、高度、角度测量',
                category: 'measurement',
                icon: this._getIconPath('测量工具', 'measure.svg'),
                shortcuts: ['Ctrl+M', 'M'],
                position: 1,
                panel: {
                    title: '测量工具',
                    width: 280,
                    height: 'auto',
                    template: 'measurement-panel'
                },
                features: ['distance', 'area', 'height', 'angle', 'triangle'],
                enabled: true
            },

            // 搜索功能
            search: {
                id: 'search',
                name: '搜索定位',
                tooltip: '坐标搜索和地名定位',
                category: 'navigation',
                icon: this._getIconPath('搜索功能', 'search.svg'),
                shortcuts: ['Ctrl+F', 'F'],
                position: 2,
                panel: {
                    title: '搜索定位',
                    width: 300,
                    height: 'auto',
                    template: 'search-panel'
                },
                features: ['coordinate', 'placename', 'bookmark'],
                enabled: true
            },

            // 地形开挖
            'terrain-dig': {
                id: 'terrain-dig',
                name: '地形开挖',
                tooltip: '地形裁剪和开挖分析',
                category: 'terrain',
                icon: this._getIconPath('地形开挖', 'terrain-dig.svg'),
                shortcuts: ['Ctrl+D'],
                position: 3,
                panel: {
                    title: '地形开挖',
                    width: 260,
                    height: 'auto',
                    template: 'terrain-panel'
                },
                features: ['polygon-dig', 'volume-calc', 'texture-mapping'],
                enabled: true
            },

            // 剖面分析
            'profile-analysis': {
                id: 'profile-analysis',
                name: '剖面分析',
                tooltip: '地形高程剖面分析',
                category: 'terrain',
                icon: this._getIconPath('剖面分析', 'profile-analysis.svg'),
                shortcuts: ['Ctrl+P'],
                position: 4,
                panel: {
                    title: '剖面分析',
                    width: 350,
                    height: 400,
                    template: 'profile-panel'
                },
                features: ['line-profile', 'elevation-chart', 'export'],
                enabled: true
            },

            // 书签管理
            bookmark: {
                id: 'bookmark',
                name: '书签管理',
                tooltip: '视点书签保存和管理',
                category: 'management',
                icon: this._getIconPath('书签管理', 'bookmark.svg'),
                shortcuts: ['Ctrl+B', 'B'],
                position: 5,
                panel: {
                    title: '书签管理',
                    width: 320,
                    height: 450,
                    template: 'bookmark-panel'
                },
                features: ['save-view', 'bookmark-list', 'preview'],
                enabled: true
            },

            // 漫游飞行
            'roam-fly': {
                id: 'roam-fly',
                name: '漫游飞行',
                tooltip: '路径飞行和场景漫游',
                category: 'navigation',
                icon: this._getIconPath('漫游飞行', 'roamfly-icon.svg'),
                shortcuts: ['Ctrl+R'],
                position: 6,
                panel: {
                    title: '漫游飞行',
                    width: 300,
                    height: 350,
                    template: 'roamfly-panel'
                },
                features: ['path-design', 'animation-control', 'speed-adjust'],
                enabled: true
            },

            // 打印功能
            print: {
                id: 'print',
                name: '打印输出',
                tooltip: '场景截图和打印输出',
                category: 'tools',
                icon: this._getIconPath('打印功能', 'print.svg'),
                shortcuts: ['Ctrl+Shift+P'],
                position: 7,
                panel: {
                    title: '打印输出',
                    width: 280,
                    height: 'auto',
                    template: 'print-panel'
                },
                features: ['screenshot', 'print-setup', 'export'],
                enabled: true
            },

            // 标记管理
            'add-marker': {
                id: 'add-marker',
                name: '标记管理',
                tooltip: '地图标记点添加和管理',
                category: 'management',
                icon: this._getIconPath('标记管理', 'marker.svg'),
                shortcuts: ['Ctrl+Shift+M'],
                position: 8,
                panel: {
                    title: '标记管理',
                    width: 300,
                    height: 400,
                    template: 'marker-panel'
                },
                features: ['add-marker', 'marker-list', 'marker-edit'],
                enabled: true
            },

            // 场景管理
            'scene-manager': {
                id: 'scene-manager',
                name: '场景管理',
                tooltip: '场景状态保存和恢复',
                category: 'management',
                icon: this._getIconPath('场景管理', 'scene-manager.svg'),
                shortcuts: ['Ctrl+S'],
                position: 9,
                panel: {
                    title: '场景管理',
                    width: 320,
                    height: 400,
                    template: 'scene-panel'
                },
                features: ['save-scene', 'scene-list', 'scene-restore'],
                enabled: true
            },

            // 三维建筑
            'building-3d': {
                id: 'building-3d',
                name: '三维建筑',
                tooltip: '三维建筑物添加和编辑',
                category: 'visualization',
                icon: this._getIconPath('三维建筑', 'building.svg'),
                shortcuts: ['Ctrl+Shift+B'],
                position: 10,
                panel: {
                    title: '三维建筑',
                    width: 280,
                    height: 'auto',
                    template: 'building-panel'
                },
                features: ['add-building', 'building-types', 'building-edit'],
                enabled: true
            }
        };
    }

    /**
     * 构建图标完整路径
     */
    _getIconPath(toolFolder, iconFile) {
        return `${this.basePaths.buttons}/${toolFolder}/svg/${iconFile}`;
    }

    /**
     * 获取单个按钮配置
     */
    getConfig(buttonId) {
        const config = this.configs[buttonId];
        if (!config) {
            console.warn(`ButtonConfig: 未找到按钮配置 "${buttonId}"`);
            return null;
        }
        return { ...config }; // 返回副本防止意外修改
    }

    /**
     * 获取所有按钮配置
     */
    getAllConfigs() {
        return { ...this.configs }; // 返回副本
    }

    /**
     * 按分类获取按钮配置
     */
    getConfigsByCategory(category) {
        const configs = {};
        Object.entries(this.configs).forEach(([id, config]) => {
            if (config.category === category) {
                configs[id] = { ...config };
            }
        });
        return configs;
    }

    /**
     * 按位置排序获取按钮配置
     */
    getConfigsByOrder() {
        return Object.entries(this.configs)
            .filter(([_, config]) => config.enabled)
            .sort(([_, a], [__, b]) => a.position - b.position)
            .map(([id, config]) => ({ id, ...config }));
    }

    /**
     * 获取分类信息
     */
    getCategory(categoryId) {
        return this.categories[categoryId] || null;
    }

    /**
     * 获取所有分类
     */
    getAllCategories() {
        return { ...this.categories };
    }

    /**
     * 动态添加按钮配置
     */
    addConfig(buttonId, config) {
        if (this.configs[buttonId]) {
            console.warn(`ButtonConfig: 按钮 "${buttonId}" 已存在，将被覆盖`);
        }

        // 验证必需字段
        const required = ['name', 'tooltip', 'category', 'icon'];
        const missing = required.filter(field => !config[field]);
        if (missing.length > 0) {
            throw new Error(`ButtonConfig: 缺少必需字段: ${missing.join(', ')}`);
        }

        // 设置默认值
        const defaultConfig = {
            id: buttonId,
            position: Object.keys(this.configs).length + 1,
            shortcuts: [],
            panel: {
                title: config.name,
                width: 300,
                height: 'auto',
                template: 'default-panel'
            },
            features: [],
            enabled: true
        };

        this.configs[buttonId] = { ...defaultConfig, ...config, id: buttonId };
        console.log(`✓ 按钮配置添加: ${buttonId}`);
    }

    /**
     * 更新按钮配置
     */
    updateConfig(buttonId, updates) {
        if (!this.configs[buttonId]) {
            throw new Error(`ButtonConfig: 按钮 "${buttonId}" 不存在`);
        }

        // 深度合并配置
        this.configs[buttonId] = this._deepMerge(this.configs[buttonId], updates);
        console.log(`✓ 按钮配置更新: ${buttonId}`);
    }

    /**
     * 删除按钮配置
     */
    removeConfig(buttonId) {
        if (this.configs[buttonId]) {
            delete this.configs[buttonId];
            console.log(`✓ 按钮配置删除: ${buttonId}`);
        }
    }

    /**
     * 启用/禁用按钮
     */
    setEnabled(buttonId, enabled) {
        if (this.configs[buttonId]) {
            this.configs[buttonId].enabled = enabled;
            console.log(`✓ 按钮${enabled ? '启用' : '禁用'}: ${buttonId}`);
        }
    }

    /**
     * 批量设置按钮顺序
     */
    setButtonOrder(orderedIds) {
        orderedIds.forEach((buttonId, index) => {
            if (this.configs[buttonId]) {
                this.configs[buttonId].position = index + 1;
            }
        });
        console.log('✓ 按钮顺序已更新');
    }

    /**
     * 根据快捷键查找按钮
     */
    findByShortcut(shortcut) {
        for (const [id, config] of Object.entries(this.configs)) {
            if (config.shortcuts && config.shortcuts.includes(shortcut)) {
                return { id, ...config };
            }
        }
        return null;
    }

    /**
     * 获取主题配置
     */
    getThemeConfig(theme = 'default') {
        const themes = {
            default: {
                buttonSize: 48,
                buttonSpacing: 15,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                hoverColor: '#2196F3',
                activeColor: '#1976D2'
            },
            compact: {
                buttonSize: 36,
                buttonSpacing: 10,
                borderRadius: '4px',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                hoverColor: '#2196F3',
                activeColor: '#1976D2'
            },
            dark: {
                buttonSize: 48,
                buttonSpacing: 15,
                borderRadius: '50%',
                backgroundColor: 'rgba(33, 33, 33, 0.9)',
                hoverColor: '#4CAF50',
                activeColor: '#388E3C'
            }
        };
        return themes[theme] || themes.default;
    }

    /**
     * 深度合并对象
     */
    _deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this._deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        return result;
    }

    /**
     * 导出配置到JSON
     */
    exportConfig() {
        return JSON.stringify({
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            basePaths: this.basePaths,
            categories: this.categories,
            configs: this.configs
        }, null, 2);
    }

    /**
     * 从JSON导入配置
     */
    importConfig(jsonString) {
        try {
            const data = JSON.parse(jsonString);
            if (data.configs) {
                this.configs = data.configs;
            }
            if (data.categories) {
                this.categories = data.categories;
            }
            if (data.basePaths) {
                this.basePaths = data.basePaths;
            }
            console.log('✓ 配置导入成功');
        } catch (error) {
            throw new Error(`ButtonConfig: 配置导入失败: ${error.message}`);
        }
    }

    /**
     * 验证配置完整性
     */
    validate() {
        const errors = [];
        
        Object.entries(this.configs).forEach(([id, config]) => {
            if (!config.name) errors.push(`${id}: 缺少名称`);
            if (!config.tooltip) errors.push(`${id}: 缺少提示文本`);
            if (!config.category) errors.push(`${id}: 缺少分类`);
            if (!config.icon) errors.push(`${id}: 缺少图标路径`);
            if (!this.categories[config.category]) {
                errors.push(`${id}: 未知分类 "${config.category}"`);
            }
        });

        if (errors.length > 0) {
            console.warn('ButtonConfig 配置验证失败:', errors);
        }

        return errors;
    }
}

// 创建全局单例实例
window.ButtonConfig = new ButtonConfig();