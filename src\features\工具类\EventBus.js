/**
 * 事件总线系统 - 解耦模块间依赖，提供统一的事件通信机制
 * 解决模块间直接调用、全局变量依赖、通信复杂的问题
 */
class EventBus {
    constructor() {
        this.events = new Map(); // 事件监听器映射
        this.middlewares = []; // 事件中间件
        this.eventHistory = []; // 事件历史记录
        this.maxHistorySize = 1000; // 最大历史记录数
        this.debugMode = false; // 调试模式
        
        // 事件统计
        this.stats = {
            emitted: 0,
            listened: 0,
            errors: 0
        };

        console.log('✓ EventBus 事件总线系统初始化完成');
    }

    /**
     * 监听事件
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} options - 选项
     * @returns {Function} 取消监听的函数
     */
    on(eventName, callback, options = {}) {
        if (typeof eventName !== 'string' || typeof callback !== 'function') {
            throw new Error('EventBus: 事件名称必须是字符串，回调必须是函数');
        }

        const {
            once = false,           // 是否只监听一次
            priority = 0,           // 优先级（数字越大优先级越高）
            context = null,         // 执行上下文
            filter = null,          // 事件过滤器
            namespace = null        // 命名空间
        } = options;

        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }

        const listener = {
            id: `${eventName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            callback,
            once,
            priority,
            context,
            filter,
            namespace,
            createTime: Date.now(),
            callCount: 0
        };

        // 按优先级插入
        const listeners = this.events.get(eventName);
        const insertIndex = listeners.findIndex(l => l.priority < priority);
        if (insertIndex === -1) {
            listeners.push(listener);
        } else {
            listeners.splice(insertIndex, 0, listener);
        }

        this.stats.listened++;
        
        if (this.debugMode) {
            console.log(`EventBus: 监听事件 "${eventName}", ID: ${listener.id}`);
        }

        // 返回取消监听函数
        return () => this.off(eventName, listener.id);
    }

    /**
     * 监听一次事件
     */
    once(eventName, callback, options = {}) {
        return this.on(eventName, callback, { ...options, once: true });
    }

    /**
     * 取消监听事件
     */
    off(eventName, listenerIdOrCallback) {
        if (!this.events.has(eventName)) return false;

        const listeners = this.events.get(eventName);
        let removedCount = 0;

        if (typeof listenerIdOrCallback === 'string') {
            // 按ID移除
            const index = listeners.findIndex(l => l.id === listenerIdOrCallback);
            if (index !== -1) {
                listeners.splice(index, 1);
                removedCount = 1;
            }
        } else if (typeof listenerIdOrCallback === 'function') {
            // 按回调函数移除
            for (let i = listeners.length - 1; i >= 0; i--) {
                if (listeners[i].callback === listenerIdOrCallback) {
                    listeners.splice(i, 1);
                    removedCount++;
                }
            }
        } else {
            // 移除所有监听器
            removedCount = listeners.length;
            listeners.length = 0;
        }

        // 如果没有监听器了，删除事件
        if (listeners.length === 0) {
            this.events.delete(eventName);
        }

        if (this.debugMode && removedCount > 0) {
            console.log(`EventBus: 移除 ${removedCount} 个 "${eventName}" 监听器`);
        }

        return removedCount > 0;
    }

    /**
     * 发送事件
     */
    emit(eventName, data = null, options = {}) {
        const {
            async = false,          // 是否异步发送
            timeout = 5000,         // 超时时间（毫秒）
            stopOnError = false,    // 是否在错误时停止
            metadata = {}           // 事件元数据
        } = options;

        const eventData = {
            name: eventName,
            data,
            metadata,
            timestamp: Date.now(),
            id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        this.stats.emitted++;

        // 记录事件历史
        this._recordEvent(eventData);

        if (this.debugMode) {
            console.log(`EventBus: 发送事件 "${eventName}"`, eventData);
        }

        // 应用中间件
        const processedEventData = this._applyMiddlewares(eventData);

        if (!this.events.has(eventName)) {
            if (this.debugMode) {
                console.warn(`EventBus: 没有监听器监听事件 "${eventName}"`);
            }
            return Promise.resolve([]);
        }

        const listeners = this.events.get(eventName).slice(); // 复制数组避免修改
        const results = [];

        if (async) {
            return this._emitAsync(listeners, processedEventData, { timeout, stopOnError });
        } else {
            return this._emitSync(listeners, processedEventData, { stopOnError });
        }
    }

    /**
     * 同步发送事件
     */
    _emitSync(listeners, eventData, options) {
        const results = [];
        const { stopOnError } = options;

        for (const listener of listeners) {
            try {
                // 应用过滤器
                if (listener.filter && !listener.filter(eventData)) {
                    continue;
                }

                listener.callCount++;
                
                let result;
                if (listener.context) {
                    result = listener.callback.call(listener.context, eventData);
                } else {
                    result = listener.callback(eventData);
                }
                
                results.push({
                    listenerId: listener.id,
                    result,
                    success: true
                });

                // 如果是一次性监听器，移除它
                if (listener.once) {
                    this.off(eventData.name, listener.id);
                }

            } catch (error) {
                this.stats.errors++;
                const errorResult = {
                    listenerId: listener.id,
                    error,
                    success: false
                };
                results.push(errorResult);

                if (this.debugMode) {
                    console.error(`EventBus: 事件 "${eventData.name}" 监听器错误`, error);
                }

                if (stopOnError) {
                    break;
                }
            }
        }

        return results;
    }

    /**
     * 异步发送事件
     */
    async _emitAsync(listeners, eventData, options) {
        const { timeout, stopOnError } = options;
        const results = [];

        for (const listener of listeners) {
            try {
                // 应用过滤器
                if (listener.filter && !listener.filter(eventData)) {
                    continue;
                }

                listener.callCount++;

                const promise = listener.context 
                    ? listener.callback.call(listener.context, eventData)
                    : listener.callback(eventData);

                // 添加超时控制
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('事件处理超时')), timeout);
                });

                const result = await Promise.race([promise, timeoutPromise]);
                
                results.push({
                    listenerId: listener.id,
                    result,
                    success: true
                });

                // 如果是一次性监听器，移除它
                if (listener.once) {
                    this.off(eventData.name, listener.id);
                }

            } catch (error) {
                this.stats.errors++;
                const errorResult = {
                    listenerId: listener.id,
                    error,
                    success: false
                };
                results.push(errorResult);

                if (this.debugMode) {
                    console.error(`EventBus: 事件 "${eventData.name}" 异步监听器错误`, error);
                }

                if (stopOnError) {
                    break;
                }
            }
        }

        return results;
    }

    /**
     * 添加中间件
     */
    use(middleware) {
        if (typeof middleware !== 'function') {
            throw new Error('EventBus: 中间件必须是函数');
        }
        this.middlewares.push(middleware);
        console.log('EventBus: 中间件已添加');
    }

    /**
     * 应用中间件
     */
    _applyMiddlewares(eventData) {
        return this.middlewares.reduce((data, middleware) => {
            try {
                return middleware(data) || data;
            } catch (error) {
                console.error('EventBus: 中间件执行错误', error);
                return data;
            }
        }, eventData);
    }

    /**
     * 命名空间操作
     */
    namespace(ns) {
        return {
            on: (eventName, callback, options = {}) => 
                this.on(`${ns}:${eventName}`, callback, { ...options, namespace: ns }),
            once: (eventName, callback, options = {}) => 
                this.once(`${ns}:${eventName}`, callback, { ...options, namespace: ns }),
            off: (eventName, listener) => 
                this.off(`${ns}:${eventName}`, listener),
            emit: (eventName, data, options = {}) => 
                this.emit(`${ns}:${eventName}`, data, options),
            clear: () => this.clearNamespace(ns)
        };
    }

    /**
     * 清除命名空间
     */
    clearNamespace(namespace) {
        let removedCount = 0;
        for (const [eventName, listeners] of this.events.entries()) {
            if (eventName.startsWith(`${namespace}:`)) {
                removedCount += listeners.length;
                this.events.delete(eventName);
            } else {
                // 移除命名空间下的监听器
                const filteredListeners = listeners.filter(l => l.namespace !== namespace);
                removedCount += listeners.length - filteredListeners.length;
                if (filteredListeners.length === 0) {
                    this.events.delete(eventName);
                } else {
                    this.events.set(eventName, filteredListeners);
                }
            }
        }
        console.log(`EventBus: 清除命名空间 "${namespace}"，移除 ${removedCount} 个监听器`);
    }

    /**
     * 等待事件
     */
    waitFor(eventName, timeout = 5000, filter = null) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                this.off(eventName, listener);
                reject(new Error(`等待事件 "${eventName}" 超时`));
            }, timeout);

            const listener = this.once(eventName, (eventData) => {
                clearTimeout(timeoutId);
                resolve(eventData);
            }, { filter });
        });
    }

    /**
     * 批量发送事件
     */
    emitBatch(events, options = {}) {
        const results = [];
        for (const event of events) {
            const { name, data, eventOptions = {} } = event;
            const result = this.emit(name, data, { ...options, ...eventOptions });
            results.push({ eventName: name, result });
        }
        return results;
    }

    /**
     * 事件历史记录
     */
    _recordEvent(eventData) {
        this.eventHistory.unshift({
            ...eventData,
            recordTime: Date.now()
        });

        // 限制历史记录大小
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory = this.eventHistory.slice(0, this.maxHistorySize);
        }
    }

    /**
     * 获取事件历史
     */
    getEventHistory(eventName = null, limit = 50) {
        let history = this.eventHistory.slice(0, limit);
        if (eventName) {
            history = history.filter(event => event.name === eventName);
        }
        return history;
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            activeListeners: Array.from(this.events.values()).reduce((sum, listeners) => sum + listeners.length, 0),
            activeEvents: this.events.size,
            historySize: this.eventHistory.length
        };
    }

    /**
     * 获取监听器信息
     */
    getListeners(eventName = null) {
        const result = {};
        
        if (eventName) {
            result[eventName] = this.events.get(eventName)?.map(l => ({
                id: l.id,
                priority: l.priority,
                once: l.once,
                namespace: l.namespace,
                callCount: l.callCount,
                createTime: l.createTime
            })) || [];
        } else {
            for (const [name, listeners] of this.events.entries()) {
                result[name] = listeners.map(l => ({
                    id: l.id,
                    priority: l.priority,
                    once: l.once,
                    namespace: l.namespace,
                    callCount: l.callCount,
                    createTime: l.createTime
                }));
            }
        }
        
        return result;
    }

    /**
     * 设置调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`EventBus: 调试模式${enabled ? '开启' : '关闭'}`);
    }

    /**
     * 清空所有监听器
     */
    clear() {
        const listenerCount = Array.from(this.events.values()).reduce((sum, listeners) => sum + listeners.length, 0);
        this.events.clear();
        console.log(`EventBus: 清空所有监听器 (${listenerCount} 个)`);
    }

    /**
     * 销毁事件总线
     */
    destroy() {
        this.clear();
        this.middlewares.length = 0;
        this.eventHistory.length = 0;
        this.stats = { emitted: 0, listened: 0, errors: 0 };
        console.log('EventBus: 事件总线已销毁');
    }
}

// 创建全局事件总线实例
window.EventBus = new EventBus();

// 为常用功能创建快捷别名
window.$on = window.EventBus.on.bind(window.EventBus);
window.$off = window.EventBus.off.bind(window.EventBus);
window.$emit = window.EventBus.emit.bind(window.EventBus);
window.$once = window.EventBus.once.bind(window.EventBus);

/**
 * 预定义的系统事件常量
 */
window.EVENTS = {
    // 工具栏事件
    TOOLBAR: {
        BUTTON_CREATED: 'toolbar:button:created',
        BUTTON_CLICKED: 'toolbar:button:clicked',
        BUTTON_ACTIVATED: 'toolbar:button:activated',
        BUTTON_DEACTIVATED: 'toolbar:button:deactivated',
        TOOL_REGISTERED: 'toolbar:tool:registered',
        TOOL_UNREGISTERED: 'toolbar:tool:unregistered'
    },

    // 面板事件
    PANEL: {
        CREATED: 'panel:created',
        SHOWN: 'panel:shown',
        CLOSED: 'panel:closed',
        DESTROYED: 'panel:destroyed',
        ACTION: 'panel:action',
        INPUT: 'panel:input',
        RESIZED: 'panel:resized',
        MOVED: 'panel:moved'
    },

    // 工具事件
    TOOL: {
        MEASURE_START: 'tool:measure:start',
        MEASURE_END: 'tool:measure:end',
        MEASURE_CLEAR: 'tool:measure:clear',
        SEARCH_QUERY: 'tool:search:query',
        SEARCH_RESULT: 'tool:search:result',
        BOOKMARK_SAVE: 'tool:bookmark:save',
        BOOKMARK_LOAD: 'tool:bookmark:load',
        TERRAIN_DIG_START: 'tool:terrain:dig:start',
        TERRAIN_DIG_END: 'tool:terrain:dig:end',
        PROFILE_ANALYZE: 'tool:profile:analyze',
        MARKER_ADD: 'tool:marker:add',
        MARKER_REMOVE: 'tool:marker:remove',
        SCENE_SAVE: 'tool:scene:save',
        SCENE_LOAD: 'tool:scene:load',
        PRINT_START: 'tool:print:start',
        ROAM_FLY_START: 'tool:roamfly:start',
        ROAM_FLY_STOP: 'tool:roamfly:stop'
    },

    // 系统事件
    SYSTEM: {
        READY: 'system:ready',
        ERROR: 'system:error',
        WARNING: 'system:warning',
        CONFIG_CHANGED: 'system:config:changed',
        THEME_CHANGED: 'system:theme:changed'
    },

    // Cesium事件
    CESIUM: {
        VIEWER_READY: 'cesium:viewer:ready',
        CAMERA_MOVE: 'cesium:camera:move',
        CAMERA_MOVE_END: 'cesium:camera:move:end',
        PICK: 'cesium:pick',
        DOUBLE_CLICK: 'cesium:double:click'
    }
};

console.log('✓ EventBus 全局实例和事件常量已创建');