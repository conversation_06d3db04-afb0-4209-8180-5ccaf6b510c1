/* 路线漫游面板样式 */
.roamfly-panel {
    width: 300px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* 定制滚动条 */
.roamfly-panel ::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

.roamfly-panel ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.roamfly-panel ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

/* 面板容器 */
.roamfly-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 面板头部 */
.roamfly-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background: #2b8df7;
    color: white;
    border-radius: 4px 4px 0 0;
}

.roamfly-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
}

.roamfly-header .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

/* 面板内容 */
.roamfly-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 12px;
    max-height: calc(100vh - 120px);
}

/* 区块样式 */
.control-section, .info-section {
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding-bottom: 8px;
}

.control-section:last-child, .info-section:last-child {
    border-bottom: none;
}

/* 小标题 */
.control-section h4, .info-section h4 {
    font-size: 13px;
    color: #2b8df7;
    margin: 0 0 6px 0;
    font-weight: 500;
}

/* 控制项 */
.control-item, .info-item {
    margin-bottom: 6px;
    display: flex;
    flex-direction: column;
}

.control-item label, .info-item label {
    margin-bottom: 2px;
    font-size: 12px;
    color: #333;
}

.value-display {
    font-size: 12px;
    color: #333;
    padding: 2px 0;
}

/* 表单样式 */
.form-control {
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    padding: 4px 6px;
    font-size: 12px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    transition: border-color 0.2s ease;
}

input.form-control:focus, 
select.form-control:focus {
    border-color: #2b8df7;
    outline: none;
    box-shadow: 0 0 0 2px rgba(43, 141, 247, 0.2);
}

/* 滑块样式 */
input[type="range"].form-control {
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
    width: 100%;
    height: 5px;
    background: #e4e7ed;
    border-radius: 3px;
    border: none;
    margin-top: 6px;
}

input[type="range"].form-control::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 14px;
    height: 14px;
    background: #2b8df7;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Firefox支持 */
input[type="range"].form-control::-moz-range-thumb {
    -moz-appearance: none;
    width: 14px;
    height: 14px;
    background: #2b8df7;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Edge/IE支持 */
input[type="range"].form-control::-ms-thumb {
    -ms-appearance: none;
    width: 14px;
    height: 14px;
    background: #2b8df7;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* 按钮样式 */
.button-group {
    display: flex;
    gap: 6px;
    margin-top: 6px;
}

.btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.btn-primary {
    background-color: #2b8df7;
    color: white;
}

.btn-primary:hover {
    background-color: #2180e3;
}

.btn-danger {
    background-color: #f56c6c;
    color: white;
}

.btn-danger:hover {
    background-color: #e64242;
}

.btn:disabled {
    background-color: #c0c4cc;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 进度条 */
.progress {
    height: 6px;
    background-color: #e4e7ed;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 4px;
}

.progress-bar {
    height: 100%;
    background-color: #2b8df7;
    transition: width 0.3s ease;
}

/* 信息显示区域 */
.info-item .value-display {
    margin-left: 0;
    font-weight: normal;
}

/* 紧凑信息布局 */
.info-section .info-item {
    margin-bottom: 3px;
}

/* 实时信息区域布局优化 */
.info-section:last-child {
    display: block;
}

/* 实时信息网格布局 */
.info-section .grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px 8px;
}

/* 确保所有值都左对齐 */
.info-item .value-display {
    margin-left: 0;
    font-weight: normal;
    text-align: left;
}

/* 进度条样式调整 */
.info-section .info-item:last-child {
    margin-top: 6px;
    width: 100%;
} 