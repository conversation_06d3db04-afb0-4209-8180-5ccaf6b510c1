/**
 * 剖面分析UI组件
 * 提供剖面分析功能的界面和事件处理
 */
class ProfileAnalysisUI {
    /**
     * 创建剖面分析UI组件
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.profileAnalysis = null;
        this.panelId = 'profileAnalysisPanel';
        this.toggleBtnId = 'toggleProfileAnalysis';
    }

    /**
     * 生成剖面分析组件的HTML代码
     * @returns {string} HTML代码字符串
     */
    generateHTML() {
        return `
        <!-- 剖面分析按钮 -->
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/剖面分析/assets/svg/profile-analysis.svg" alt="剖面分析">
            <div class="tooltip">剖面分析</div>
        </button>
        
        <!-- 剖面分析面板 -->
        <div class="profile-panel" id="${this.panelId}" style="display: none;">
            <div class="toolbar-panel-title">剖面分析</div>
            <div class="submergence-data">
                <div class="toolbar-data-item">
                    <label>起点坐标</label>
                    <div class="value" id="startCoord">-</div>
                </div>
                <div class="toolbar-data-item">
                    <label>终点坐标</label>
                    <div class="value" id="endCoord">-</div>
                </div>
                <div class="toolbar-data-item">
                    <label>剖面长度</label>
                    <div class="value" id="profileLength">-</div>
                </div>
            </div>
            <div id="profileChart" style="width: 100%; height: 250px;"></div>
            <div class="button-group">
                <button class="primary-btn" id="drawProfile">
                    <img src="src/features/按钮/剖面分析/assets/svg/draw.svg" alt="绘制">
                    绘制剖面线
                </button>
                <button class="secondary-btn" id="clearProfile">
                    <img src="src/features/按钮/剖面分析/assets/svg/clear.svg" alt="清除">
                    清除
                </button>
                <button class="danger-btn" id="exitProfile">
                    <img src="src/features/按钮/剖面分析/assets/svg/close.svg" alt="退出">
                    退出分析
                </button>
            </div>
        </div>`;
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/剖面分析/assets/svg/profile-analysis.svg" alt="剖面分析">
            <div class="tooltip">剖面分析</div>
        </button>`;
        
        container.appendChild(tempDiv.firstElementChild);
        console.log(`剖面分析按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 剖面分析面板 -->
        <div class="profile-panel" id="${this.panelId}" style="display: none;">
            <div class="toolbar-panel-title">剖面分析</div>
            <div class="submergence-data">
                <div class="toolbar-data-item">
                    <label>起点坐标</label>
                    <div class="value" id="startCoord">-</div>
                </div>
                <div class="toolbar-data-item">
                    <label>终点坐标</label>
                    <div class="value" id="endCoord">-</div>
                </div>
                <div class="toolbar-data-item">
                    <label>剖面长度</label>
                    <div class="value" id="profileLength">-</div>
                </div>
            </div>
            <div id="profileChart" style="width: 100%; height: 250px;"></div>
            <div class="button-group">
                <button class="primary-btn" id="drawProfile">
                    <img src="src/features/按钮/剖面分析/assets/svg/draw.svg" alt="绘制">
                    绘制剖面线
                </button>
                <button class="secondary-btn" id="clearProfile">
                    <img src="src/features/按钮/剖面分析/assets/svg/clear.svg" alt="清除">
                    清除
                </button>
                <button class="danger-btn" id="exitProfile">
                    <img src="src/features/按钮/剖面分析/assets/svg/close.svg" alt="退出">
                    退出分析
                </button>
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        console.log('剖面分析面板已添加到页面');
    }

    /**
     * 初始化剖面分析组件及事件绑定
     */
    init() {
        console.log('正在初始化剖面分析UI组件...');
        
        // 获取或创建剖面分析实例
        this.profileAnalysis = window.profileAnalysis || ProfileAnalysis.init(this.viewer);
        window.profileAnalysis = this.profileAnalysis;
        
        // 切换面板显示/隐藏的全局函数
        window.toggleProfilePanel = () => {
            console.log('直接调用toggleProfilePanel函数');
            const panel = document.getElementById(this.panelId);
            if (panel) {
                const currentDisplay = window.getComputedStyle(panel).display;
                console.log('剖面分析面板当前显示状态:', currentDisplay);
                
                if (currentDisplay === 'none') {
                    console.log('直接显示剖面分析面板');
                    
                    // 隐藏其他面板
                    ['searchPanel', 'terrainDigPanel', 'measureToolContainer'].forEach(id => {
                        const otherPanel = document.getElementById(id);
                        if (otherPanel) {
                            otherPanel.style.display = 'none';
                            console.log(`已隐藏面板: ${id}`);
                        }
                    });
                    
                    // 设置面板位置并显示
                    this.showPanel();
                } else {
                    console.log('直接隐藏剖面分析面板');
                    this.hidePanel();
                }
            } else {
                console.error('剖面分析面板元素不存在');
            }
        };
        
        // 绑定剖面分析按钮事件
        this.bindEvents();
        
        console.log('剖面分析UI组件初始化完成');
    }

    /**
     * 显示面板并设置位置
     */
    showPanel() {
        console.log('显示剖面分析面板');
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (panel) {
            panel.style.display = 'block';
            
            if (toggleBtn && typeof PanelPositioner !== 'undefined') {
                try {
                    console.log('正在使用PanelPositioner设置面板位置');
                    PanelPositioner.setPosition(toggleBtn, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                    console.log('面板位置设置完成');
                } catch (e) {
                    console.error('面板定位出错:', e);
                    // 默认位置
                    panel.style.position = 'fixed';
                    panel.style.top = '100px';
                    panel.style.left = '100px';
                }
            } else {
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
            }
            
            // 如果存在剖面分析实例，激活它
            if (this.profileAnalysis && typeof this.profileAnalysis.activate === 'function') {
                this.profileAnalysis.activate();
            }
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        const panel = document.getElementById(this.panelId);
        if (panel) {
            panel.style.display = 'none';
            
            // 如果存在剖面分析实例，停用它
            if (this.profileAnalysis && typeof this.profileAnalysis.deactivate === 'function') {
                this.profileAnalysis.deactivate();
            }
        }
    }

    /**
     * 绑定事件处理函数
     */
    bindEvents() {
        // 绑定切换按钮事件
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            console.log('绑定剖面分析切换按钮事件');
            
            // 清除可能存在的事件绑定
            const newToggleBtn = toggleBtn.cloneNode(true);
            if (toggleBtn.parentNode) {
                toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
                console.log('已清除并替换剖面分析按钮以避免事件重复绑定');
            }
            
            newToggleBtn.addEventListener('click', () => {
                console.log('剖面分析按钮被点击 (ProfileAnalysisUI事件)');
                window.toggleProfilePanel();
            });
        }
        
        // 其他按钮事件在ProfileAnalysis.init中已经处理
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {ProfileAnalysisUI} 剖面分析UI实例
     */
    static init(viewer, toolButtonsId = 'toolButtons') {
        console.log('ProfileAnalysisUI.init 被调用');
        const profileAnalysisUI = new ProfileAnalysisUI(viewer);
        
        // 检查是否需要添加HTML元素
        if (!document.getElementById(profileAnalysisUI.toggleBtnId)) {
            profileAnalysisUI.appendButtonTo(toolButtonsId);
        }
        
        if (!document.getElementById(profileAnalysisUI.panelId)) {
            profileAnalysisUI.appendPanelToBody();
        }
        
        // 初始化剖面分析组件
        profileAnalysisUI.init();
        
        return profileAnalysisUI;
    }
}

// 导出到全局作用域
window.ProfileAnalysisUI = ProfileAnalysisUI; 