/**
 * 测量工具模块入口文件
 * @module MeasureModule
 */

// 导入核心模块
import { MeasureTool } from './core/MeasureTool.js';
import { MeasureUI } from './ui/MeasureUI.js';
import { MeasureConfig } from './config.js';

/**
 * 测量工具模块
 */
export class MeasureModule {
    constructor(viewer) {
        this.viewer = viewer;
        this.config = MeasureConfig;
        this.tool = null;
        this.ui = null;
    }

    /**
     * 初始化模块
     * @param {string} containerId - 容器ID
     * @returns {Promise<MeasureModule>}
     */
    async init(containerId = 'toolButtons') {
        try {
            // 初始化UI
            this.ui = await MeasureUI.init(this.viewer, containerId);
            
            // 获取工具实例（UI初始化时已创建）
            this.tool = this.ui.measureTool;
            
            return this;
        } catch (error) {
            console.error('测量工具模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁模块
     */
    destroy() {
        if (this.ui) {
            this.ui.destroy && this.ui.destroy();
        }
        if (this.tool) {
            this.tool.destroy && this.tool.destroy();
        }
    }
}

// 向后兼容导出
export { MeasureTool } from './core/MeasureTool.js';
export { MeasureUI } from './ui/MeasureUI.js';
export { MeasureConfig } from './config.js';

// 全局访问
window.MeasureModule = MeasureModule;