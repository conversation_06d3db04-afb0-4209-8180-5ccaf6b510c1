/**
 * 场景管理器配置
 */
export const SceneManagerConfig = {
    // 本地存储键名
    storageKey: 'cesiumSceneManager',
    
    // 场景列表默认标题
    defaultTitle: '场景列表',
    
    // 默认场景名称
    defaultSceneName: '未命名场景',
    
    // 最大保存场景数量
    maxSceneCount: 10,
    
    // UI配置
    ui: {
        // 面板宽度
        panelWidth: 300,
        
        // 按钮图标路径
        iconPath: 'src/features/按钮/场景管理/svg/scene-manager.svg',
        
        // 提示文本
        tooltipText: '场景管理'
    },
    
    // 需要保存的场景属性
    sceneProperties: {
        // 是否保存相机位置
        saveCamera: true,
        
        // 是否保存图层状态
        saveLayers: true,
        
        // 是否保存标记点
        saveMarkers: true,
        
        // 是否保存测量结果，默认禁用
        saveMeasurements: false
    }
}; 