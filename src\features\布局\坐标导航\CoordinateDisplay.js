// 坐标显示类
// 注意：CSS文件已在HTML中通过link标签引入

class CoordinateDisplay {
    constructor(viewer) {
        this.viewer = viewer;
        this.handler = null;
        this.coordinateSystem = 'WGS84'; // 默认坐标系
        this.container = null;
        this.distanceScale = null; // 比例尺值
        this.distanceBarWidth = 0; // 比例尺宽度
        
        this.initUI();
        this.initEventHandler();
        this.loadSvgIcons();
    }

    // 加载SVG图标
    loadSvgIcons() {
        const svgFiles = {
            longitude: 'src/features/布局/坐标导航/svg/longitude.svg',
            latitude: 'src/features/布局/坐标导航/svg/latitude.svg',
            altitude: 'src/features/布局/坐标导航/svg/altitude.svg',
            scale: 'src/features/布局/坐标导航/svg/scale.svg'
        };
        
        // 加载每个SVG图标
        Object.entries(svgFiles).forEach(([id, path]) => {
            fetch(path)
                .then(response => response.text())
                .then(svgContent => {
                    // 找到图标容器并插入SVG
                    const container = this.container.querySelector(`.${id}-icon`);
                    if (container) {
                        container.innerHTML = svgContent;
                    }
                })
                .catch(error => console.error(`加载SVG图标失败 (${id}):`, error));
        });
    }

    // 初始化UI
    initUI() {
        // 创建容器
        this.container = document.createElement('div');
        this.container.className = 'coordinate-display';
        this.container.innerHTML = `
            <div class="coordinate-content">
                <div class="coordinate-text">
                    <span><span class="icon longitude-icon"></span>--</span>
                    <span><span class="icon latitude-icon"></span>--</span>
                    <span><span class="icon altitude-icon"></span>--</span>
                </div>
                <select class="coordinate-system">
                    <option value="WGS84">WGS84</option>
                    <option value="DMS">度分秒</option>
                    <option value="Mercator">墨卡托</option>
                    <option value="UTM">UTM</option>
                </select>
            </div>
            <!-- 添加比例尺显示 -->
            <div class="scale-display">
                <div class="scale-icon-container">
                    <span class="icon scale-icon"></span>
                </div>
                <div class="scale-bar"></div>
                <div class="scale-text">--</div>
            </div>
        `;

        document.body.appendChild(this.container);

        // 监听坐标系切换
        this.container.querySelector('.coordinate-system').addEventListener('change', (e) => {
            this.coordinateSystem = e.target.value;
        });
    }

    // 初始化事件处理
    initEventHandler() {
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        this.handler.setInputAction(this.onMouseMove.bind(this), Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        
        // 添加相机移动事件监听，用于更新比例尺
        this.viewer.camera.moveEnd.addEventListener(() => {
            this.updateDistanceScale();
        });
        
        // 初始化时更新一次比例尺
        this.updateDistanceScale();
    }

    // 更新比例尺
    updateDistanceScale() {
        // 获取当前视图的比例尺信息
        const canvas = this.viewer.scene.canvas;
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        
        // 获取屏幕底部中心两个点的位置
        const leftPoint = new Cesium.Cartesian2(width / 2 - 100, height - 10);
        const rightPoint = new Cesium.Cartesian2(width / 2 + 100, height - 10);
        
        // 将屏幕坐标转换为地理坐标
        const leftRay = this.viewer.camera.getPickRay(leftPoint);
        const rightRay = this.viewer.camera.getPickRay(rightPoint);
        
        // 获取交点
        const leftCartesian = this.viewer.scene.globe.pick(leftRay, this.viewer.scene);
        const rightCartesian = this.viewer.scene.globe.pick(rightRay, this.viewer.scene);
        
        // 如果没有交点，无法计算距离
        if (!leftCartesian || !rightCartesian) {
            this.container.querySelector('.scale-text').textContent = '-- km';
            this.container.querySelector('.scale-bar').style.width = '60px';
            return;
        }
        
        // 计算两点间距离
        const distance = Cesium.Cartesian3.distance(leftCartesian, rightCartesian);
        
        // 根据距离选择合适的单位和比例
        let scaleDistance, unit;
        if (distance >= 1000) {
            scaleDistance = Math.round(distance / 100) / 10;
            unit = 'km';
        } else {
            scaleDistance = Math.round(distance);
            unit = 'm';
        }
        
        // 根据实际距离调整比例尺显示宽度
        const scaleWidth = 60; // 默认宽度
        
        // 更新UI
        this.container.querySelector('.scale-text').textContent = `${scaleDistance} ${unit}`;
        this.container.querySelector('.scale-bar').style.width = `${scaleWidth}px`;
    }

    // 鼠标移动事件处理
    onMouseMove(movement) {
        const cartesian = this.viewer.camera.pickEllipsoid(
            movement.endPosition,
            this.viewer.scene.globe.ellipsoid
        );

        if (cartesian) {
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            const height = this.viewer.scene.globe.getHeight(cartographic) || 0;
            
            let coordText = '';
            switch (this.coordinateSystem) {
                case 'WGS84':
                    coordText = this.formatWGS84(cartographic, height);
                    break;
                case 'DMS':
                    coordText = this.formatDMS(cartographic, height);
                    break;
                case 'Mercator':
                    coordText = this.formatMercator(cartographic, height);
                    break;
                case 'UTM':
                    coordText = this.formatUTM(cartographic, height);
                    break;
            }

            this.container.querySelector('.coordinate-text').innerHTML = coordText;
            // 重新加载SVG图标（因为innerHTML会覆盖掉之前的图标）
            this.loadSvgIcons();
        }
    }

    // WGS84 格式化
    formatWGS84(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        return `
            <span><span class="icon longitude-icon"></span>${longitude.toFixed(4)}°</span>
            <span><span class="icon latitude-icon"></span>${latitude.toFixed(4)}°</span>
            <span><span class="icon altitude-icon"></span>${Math.round(height)}m</span>
        `;
    }

    // 度分秒格式化
    formatDMS(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);

        const formatDMS = (decimal) => {
            const degrees = Math.floor(Math.abs(decimal));
            const minutes = Math.floor((Math.abs(decimal) - degrees) * 60);
            const seconds = ((Math.abs(decimal) - degrees - minutes/60) * 3600).toFixed(0);
            const direction = decimal >= 0 ? 'N' : 'S';
            return `${degrees}°${minutes}'${seconds}"${direction}`;
        };

        return `
            <span><span class="icon longitude-icon"></span>${formatDMS(longitude)}</span>
            <span><span class="icon latitude-icon"></span>${formatDMS(latitude)}</span>
            <span><span class="icon altitude-icon"></span>${Math.round(height)}m</span>
        `;
    }

    // 墨卡托格式化
    formatMercator(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        
        // 墨卡托投影转换
        const x = longitude * 20037508.34 / 180;
        const y = Math.log(Math.tan((90 + latitude) * Math.PI / 360)) / (Math.PI / 180);
        const mercatorY = y * 20037508.34 / 180;

        return `
            <span><span class="icon longitude-icon"></span>X:${Math.round(x)}</span>
            <span><span class="icon latitude-icon"></span>Y:${Math.round(mercatorY)}</span>
            <span><span class="icon altitude-icon"></span>${Math.round(height)}m</span>
        `;
    }

    // UTM格式化
    formatUTM(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);

        // UTM区带计算
        const zone = Math.floor((longitude + 180) / 6) + 1;
        const letter = 'CDEFGHJKLMNPQRSTUVWXX'[Math.floor((latitude + 80) / 8)];

        // 简单的UTM坐标计算（这是一个简化版本）
        const centralMeridian = zone * 6 - 183;
        const easting = (longitude - centralMeridian) * 111319.9;
        const northing = latitude * 111319.9;

        return `
            <span><span class="icon longitude-icon"></span>${zone}${letter}</span>
            <span><span class="icon latitude-icon"></span>E:${Math.round(easting)}</span>
            <span><span class="icon altitude-icon"></span>N:${Math.round(northing)}</span>
        `;
    }

    // 销毁
    destroy() {
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// 导出类 - 使用全局变量方式
window.CoordinateDisplay = CoordinateDisplay; 