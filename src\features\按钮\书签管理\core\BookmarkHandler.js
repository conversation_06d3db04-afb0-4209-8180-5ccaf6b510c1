/**
 * 书签管理工具类
 * 用于保存和恢复地图视图位置
 */
class BookmarkTool {
    /**
     * 构造函数
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.bookmarks = [];
        this.cookieName = "cesium_bookmarks";
        
        // 从localStorage加载保存的书签
        this.loadBookmarks();
    }
    
    /**
     * 加载保存的书签
     * @private
     */
    loadBookmarks() {
        try {
            const savedBookmarks = localStorage.getItem(this.cookieName);
            if (savedBookmarks) {
                this.bookmarks = JSON.parse(savedBookmarks);
            }
        } catch (e) {
            console.error('加载书签失败:', e);
            this.bookmarks = [];
        }
    }
    
    /**
     * 保存书签到localStorage
     * @private
     */
    saveBookmarksToStorage() {
        try {
            localStorage.setItem(this.cookieName, JSON.stringify(this.bookmarks));
        } catch (e) {
            console.error('保存书签失败:', e);
        }
    }
    
    /**
     * 获取当前视图状态
     * @returns {Object} 当前视图状态
     */
    getCurrentView() {
        // 获取当前相机位置和方向
        const camera = this.viewer.camera;
        const position = camera.position;
        const heading = camera.heading;
        const pitch = camera.pitch;
        const roll = camera.roll;
        
        // 将笛卡尔坐标转换为经纬度高度
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;
        
        return {
            longitude: longitude,
            latitude: latitude,
            height: height,
            heading: heading,
            pitch: pitch,
            roll: roll,
            timestamp: new Date().getTime()
        };
    }
    
    /**
     * 生成书签预览图
     * @returns {Promise<string>} 返回base64编码的预览图
     */
    async generatePreviewImage() {
        return new Promise((resolve) => {
            try {
                // 捕获当前画布内容作为预览图
                const canvas = this.viewer.scene.canvas;
                const dataUrl = canvas.toDataURL('image/jpeg', 0.7);
                resolve(dataUrl);
            } catch (e) {
                console.error('生成预览图失败:', e);
                // 返回默认图像
                resolve('');
            }
        });
    }
    
    /**
     * 添加书签
     * @param {string} name - 书签名称
     * @returns {Promise<Object>} 添加的书签对象
     */
    async addBookmark(name) {
        if (!name || name.trim() === '') {
            throw new Error('书签名称不能为空');
        }
        
        // 检查是否存在同名书签
        if (this.bookmarks.some(bookmark => bookmark.name === name)) {
            throw new Error('已存在同名书签');
        }
        
        // 获取当前视图和预览图
        const viewData = this.getCurrentView();
        const previewImage = await this.generatePreviewImage();
        
        // 创建书签对象
        const bookmark = {
            id: new Date().getTime(),
            name: name,
            data: viewData,
            icon: previewImage
        };
        
        // 添加到书签列表
        this.bookmarks.unshift(bookmark); // 添加到列表开头
        
        // 保存到localStorage
        this.saveBookmarksToStorage();
        
        return bookmark;
    }
    
    /**
     * 删除书签
     * @param {number} id - 书签ID
     * @returns {boolean} 是否删除成功
     */
    deleteBookmark(id) {
        const initialLength = this.bookmarks.length;
        this.bookmarks = this.bookmarks.filter(bookmark => bookmark.id !== id);
        
        if (this.bookmarks.length !== initialLength) {
            // 保存到localStorage
            this.saveBookmarksToStorage();
            return true;
        }
        return false;
    }
    
    /**
     * 获取所有书签
     * @returns {Array} 书签列表
     */
    getAllBookmarks() {
        return this.bookmarks;
    }
    
    /**
     * 跳转到指定书签位置
     * @param {Object} bookmark - 书签对象
     */
    flyToBookmark(bookmark) {
        if (!bookmark || !bookmark.data) return;
        
        const viewData = bookmark.data;
        
        // 使用flyTo动画飞行到指定位置
        this.viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(
                viewData.longitude,
                viewData.latitude,
                viewData.height
            ),
            orientation: {
                heading: viewData.heading || 0,
                pitch: viewData.pitch || -Cesium.Math.PI_OVER_TWO,
                roll: viewData.roll || 0
            },
            duration: 2.0 // 动画持续时间(秒)
        });
    }
    
    /**
     * 清空所有书签
     */
    clearAllBookmarks() {
        this.bookmarks = [];
        this.saveBookmarksToStorage();
    }
}

// 导出到全局作用域
window.BookmarkTool = BookmarkTool; 