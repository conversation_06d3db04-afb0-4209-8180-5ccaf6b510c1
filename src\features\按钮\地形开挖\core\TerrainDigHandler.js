// 地形开挖处理类
class TerrainDigHandler {
    constructor(viewer) {
        this.viewer = viewer;
        this.drawPoints = [];
        this.drawEntities = [];
        this.excavateInstance = null;
        this.isDrawing = false;
        this.currentArea = 0;
        this.positions = [];
        this.tempPositions = [];
        this.drawHandler = null;
        this.modifyHandler = null;
        this.floatingPoint = null;
        this.material = null;
        this.digEntity = null;
        this.depthValue = 30;
        
        // 确保地形深度测试开启
        this.viewer.scene.globe.depthTestAgainstTerrain = true;
    }

    // 静态初始化方法
    static init(viewer) {
        console.log('初始化地形开挖功能');
        const handler = new TerrainDigHandler(viewer);
        
        // 获取相关DOM元素
        const toggleBtn = document.getElementById('toggleTerrainDig');
        const startDigBtn = document.getElementById('startDig');
        const clearDigBtn = document.getElementById('clearDig');
        const exitDigBtn = document.getElementById('exitDig');
        const digDepthInput = document.getElementById('digDepth');
        const panel = document.getElementById('terrainDigPanel');

        // 切换按钮事件 - 注释掉此处的事件绑定，改为使用全局toggleTerrainDigPanel
        /* 
        if (toggleBtn && !toggleBtn._hasTerrainDigListener) {
            console.log('添加地形开挖按钮事件监听器');
            toggleBtn.addEventListener('click', () => {
                console.log('地形开挖按钮被点击');
                if (panel) {
                    if (panel.style.display === 'none' || !panel.style.display) {
                        // 隐藏其他面板
                        const otherPanels = ['searchPanel', 'measureToolContainer', 'profileAnalysisPanel'];
                        otherPanels.forEach(panelId => {
                            const otherPanel = document.getElementById(panelId);
                            if (otherPanel) {
                                otherPanel.style.display = 'none';
                            }
                        });
                        
                        // 显示地形开挖面板
                        handler.showPanel(toggleBtn);
                    } else {
                        handler.hidePanel();
                    }
                }
            });
            toggleBtn._hasTerrainDigListener = true;
        }
        */

        // 开始绘制按钮事件
        if (startDigBtn && !startDigBtn._hasTerrainDigListener) {
            startDigBtn.addEventListener('click', () => {
                console.log('开始绘制按钮被点击');
                handler.startDrawing();
            });
            startDigBtn._hasTerrainDigListener = true;
        }

        // 清除按钮事件
        if (clearDigBtn && !clearDigBtn._hasTerrainDigListener) {
            clearDigBtn.addEventListener('click', () => {
                console.log('清除按钮被点击');
                handler.clearAll();
            });
            clearDigBtn._hasTerrainDigListener = true;
        }

        // 退出按钮事件
        if (exitDigBtn && !exitDigBtn._hasTerrainDigListener) {
            exitDigBtn.addEventListener('click', () => {
                console.log('退出按钮被点击');
                handler.hidePanel();
                handler.clearAll();
            });
            exitDigBtn._hasTerrainDigListener = true;
        }

        // 深度滑块事件
        if (digDepthInput && !digDepthInput._hasTerrainDigListener) {
            digDepthInput.addEventListener('input', (e) => {
                const depth = parseInt(e.target.value);
                document.getElementById('depthValue').textContent = depth + '米';
                handler.depthValue = depth;
                handler.updateDigging();
            });
            digDepthInput._hasTerrainDigListener = true;
        }

        return handler;
    }

    startDrawing() {
        this.isDrawing = true;
        document.getElementById('terrainDigPanel').classList.add('drawing');
        const startBtn = document.getElementById('startDig');
        startBtn.innerHTML = '<i class="fas fa-check"></i>完成绘制';
        
        // 设置鼠标样式
        this.viewer.container.style.cursor = 'crosshair';
        
        // 创建绘制事件处理器
        this.drawHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        
        // 左键点击添加点
        this.drawHandler.setInputAction((click) => {
            const cartesian = this.viewer.scene.pickPosition(click.position);
            if (Cesium.defined(cartesian)) {
                this.drawPoints.push(cartesian);
                
                // 添加点实体
                const pointEntity = this.viewer.entities.add({
                    position: cartesian,
                    point: {
                        color: Cesium.Color.WHITE,
                        pixelSize: 10,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });
                this.drawEntities.push(pointEntity);
                
                // 如果有两个以上的点，绘制线
                if (this.drawPoints.length > 1) {
                    const lineEntity = this.viewer.entities.add({
                        polyline: {
                            positions: [this.drawPoints[this.drawPoints.length - 2], cartesian],
                            width: 2,
                            material: Cesium.Color.WHITE,
                            clampToGround: true
                        }
                    });
                    this.drawEntities.push(lineEntity);
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        
        // 右键完成绘制
        this.drawHandler.setInputAction(() => {
            if (this.drawPoints.length >= 3) {
                this.completeDrawing();
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    completeDrawing() {
        if (this.drawPoints.length < 3) {
            alert('请至少绘制3个点以形成有效区域！');
            return;
        }

        this.isDrawing = false;
        document.getElementById('terrainDigPanel').classList.remove('drawing');
        const startBtn = document.getElementById('startDig');
        startBtn.innerHTML = '<i class="fas fa-draw-polygon"></i>开始绘制';
        
        // 清除绘制事件
        if (this.drawHandler) {
            this.drawHandler.destroy();
            this.drawHandler = null;
        }
        
        // 恢复鼠标样式
        this.viewer.container.style.cursor = 'default';
        
        // 清除绘制实体
        this.drawEntities.forEach(entity => {
            this.viewer.entities.remove(entity);
        });
        this.drawEntities = [];
        
        // 开始地形开挖
        this.startExcavate();
    }

    startExcavate() {
        const depth = parseInt(document.getElementById('digDepth').value);
        const bottomTexture = document.getElementById('bottomTexture').value;
        const sideTexture = document.getElementById('sideTexture').value;
        
        // 处理坐标转换
        const cartographicPoints = [];
        
        this.drawPoints.forEach(point => {
            const cartographic = Cesium.Cartographic.fromCartesian(point);
            cartographicPoints.push({
                x: Cesium.Math.toDegrees(cartographic.longitude),
                y: Cesium.Math.toDegrees(cartographic.latitude),
                z: cartographic.height
            });
        });
        
        // 计算面积并更新体积显示
        this.currentArea = this.calculateArea(cartographicPoints);
        this.updateVolumeDisplay(this.currentArea, depth);
        
        // 确保多边形方向正确
        if (this.isClockWise(cartographicPoints)) {
            cartographicPoints.reverse();
        }
        
        this.excavate(cartographicPoints, depth, bottomTexture, sideTexture);
        
        // 清空绘制点
        this.drawPoints = [];
    }

    excavate(points, height, bottomTexture, sideTexture) {
        const nar = [];
        const hhh = [];
        
        // 收集坐标和高度
        points.forEach(point => {
            nar.push(point.x);
            nar.push(point.y);
            hhh.push(point.z);
        });
        
        // 创建裁剪平面
        const clippingPlanes = this.createClippingPlanes(points);
        this.viewer.scene.globe.clippingPlanes = new Cesium.ClippingPlaneCollection({
            planes: clippingPlanes,
            edgeWidth: 1.0,
            edgeColor: Cesium.Color.WHITE
        });
        
        // 获取最低高度
        const minHeight = Math.min(...hhh);
        
        // 创建底面
        this.viewer.entities.add({
            id: "entityDM",
            polygon: {
                hierarchy: Cesium.Cartesian3.fromDegreesArray(nar),
                material: new Cesium.ImageMaterialProperty({
                    image: bottomTexture,
                    color: new Cesium.Color.fromCssColorString("#cbc6c2"),
                    repeat: new Cesium.Cartesian2(30, 30)
                }),
                height: minHeight - height
            }
        });
        
        // 创建侧面
        const wallPositions = [];
        const maximumHeights = [];
        const minimumHeights = [];
        
        points.forEach((point, index) => {
            wallPositions.push(point.x, point.y);
            maximumHeights.push(point.z);
            minimumHeights.push(minHeight - height);
        });
        
        // 闭合墙体
        wallPositions.push(points[0].x, points[0].y);
        maximumHeights.push(points[0].z);
        minimumHeights.push(minHeight - height);
        
        this.viewer.entities.add({
            id: "entityDMBJ",
            wall: {
                positions: Cesium.Cartesian3.fromDegreesArray(wallPositions),
                maximumHeights: maximumHeights,
                minimumHeights: minimumHeights,
                material: new Cesium.ImageMaterialProperty({
                    image: sideTexture,
                    repeat: new Cesium.Cartesian2(30, 30)
                })
            }
        });
    }

    createClippingPlanes(points) {
        const clippingPlanes = [];
        const length = points.length;
        
        for (let i = 0; i < length; ++i) {
            const nextIndex = (i + 1) % length;
            const currentPoint = Cesium.Cartesian3.fromDegrees(points[i].x, points[i].y);
            const nextPoint = Cesium.Cartesian3.fromDegrees(points[nextIndex].x, points[nextIndex].y);
            
            let midpoint = Cesium.Cartesian3.add(
                currentPoint,
                nextPoint,
                new Cesium.Cartesian3()
            );
            midpoint = Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);
            
            const up = Cesium.Cartesian3.normalize(midpoint, new Cesium.Cartesian3());
            let right = Cesium.Cartesian3.subtract(
                nextPoint,
                midpoint,
                new Cesium.Cartesian3()
            );
            right = Cesium.Cartesian3.normalize(right, right);
            
            let normal = Cesium.Cartesian3.cross(right, up, new Cesium.Cartesian3());
            normal = Cesium.Cartesian3.normalize(normal, normal);
            
            const plane = new Cesium.Plane(normal, 0.0);
            const distance = Cesium.Plane.getPointDistance(plane, midpoint);
            
            clippingPlanes.push(new Cesium.ClippingPlane(normal, distance));
        }
        
        return clippingPlanes;
    }

    isClockWise(points) {
        if (points.length < 3) return false;
        
        let sum = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            sum += (points[j].x - points[i].x) * (points[j].y + points[i].y);
        }
        return sum > 0;
    }

    clearAll() {
        // 清除开挖效果
        this.viewer.entities.removeById("entityDM");
        this.viewer.entities.removeById("entityDMBJ");
        this.viewer.scene.globe.clippingPlanes = undefined;
        
        // 重置面积和体积显示
        this.currentArea = 0;
        const volumeElement = document.getElementById('volumeValue');
        if (volumeElement) {
            volumeElement.textContent = '0立方米';
        }
        
        // 清除绘制状态
        if (this.isDrawing) {
            if (this.drawHandler) {
                this.drawHandler.destroy();
                this.drawHandler = null;
            }
            this.drawEntities.forEach(entity => {
                this.viewer.entities.remove(entity);
            });
            this.drawEntities = [];
            this.drawPoints = [];
            this.isDrawing = false;
            document.getElementById('terrainDigPanel').classList.remove('drawing');
            const startBtn = document.getElementById('startDig');
            startBtn.innerHTML = '<i class="fas fa-draw-polygon"></i>开始绘制';
        }
        
        // 恢复鼠标样式
        this.viewer.container.style.cursor = 'default';
    }

    exitDigging() {
        this.clearAll();
        document.getElementById('terrainDigPanel').style.display = 'none';
    }

    calculateArea(points) {
        if (points.length < 3) return 0;
        
        let area = 0;
        const R = 6371000; // 地球平均半径（米）
        
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            const p1 = points[i];
            const p2 = points[j];
            
            // 使用Haversine公式计算两点之间的距离
            const dLon = (p2.x - p1.x) * Math.PI / 180;
            const dLat = (p2.y - p1.y) * Math.PI / 180;
            const lat1 = p1.y * Math.PI / 180;
            const lat2 = p2.y * Math.PI / 180;
            
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1) * Math.cos(lat2) * 
                    Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distance = R * c;
            
            // 计算面积
            area += distance * R * Math.cos(lat1) * dLon;
        }
        
        return Math.abs(area);
    }

    updateVolumeDisplay(area, depth) {
        const volume = area * depth;
        const volumeElement = document.getElementById('volumeValue');
        if (volumeElement) {
            if (volume > 1000000) {
                volumeElement.textContent = (volume / 1000000).toFixed(2) + '百万立方米';
            } else {
                volumeElement.textContent = volume.toFixed(2) + '立方米';
            }
        }
    }

    // 显示面板
    showPanel(button) {
        const panel = document.getElementById('terrainDigPanel');
        if (panel) {
            panel.style.display = 'block';
            console.log('正在设置地形开挖面板位置 (showPanel)');
            // 使用PanelPositioner设置面板位置
            try {
                console.log('PanelPositioner对象是否存在 (showPanel):', PanelPositioner ? true : false);
                PanelPositioner.setPosition(button, panel, {
                    preferredPosition: 'right',
                    gap: 10
                });
                console.log('面板位置设置完成 (showPanel)');
            } catch (error) {
                console.error('面板定位出错 (showPanel):', error);
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
                console.log('已使用默认位置 (showPanel)');
            }
        }
    }

    // 隐藏面板
    hidePanel() {
        const panel = document.getElementById('terrainDigPanel');
        if (panel) {
            panel.style.display = 'none';
        }
    }
}

// 全局地形开挖面板切换函数
window.toggleTerrainDigPanel = function() {
    console.log('直接调用toggleTerrainDigPanel函数');
    
    // 首先检查PanelPositioner是否存在
    var panelPositionerExists = typeof PanelPositioner !== 'undefined';
    console.log('PanelPositioner对象是否全局可用:', panelPositionerExists);
    
    const panel = document.getElementById('terrainDigPanel');
    if (panel) {
        // 更精确地检测面板状态 - 同时检查style.display和计算样式
        const styleDisplay = panel.style.display;
        const computedDisplay = window.getComputedStyle(panel).display;
        
        console.log('地形开挖面板状态:', {
            'style.display': styleDisplay,
            'computedDisplay': computedDisplay,
            '面板可见性': panel.offsetParent !== null
        });
        
        // 使用panel是否实际可见来判断
        const isActuallyVisible = computedDisplay !== 'none' && panel.offsetParent !== null;
        console.log('地形开挖面板实际状态:', isActuallyVisible ? '可见' : '隐藏');
        
        if (!isActuallyVisible) {
            console.log('直接显示地形开挖面板');
            
            // 隐藏其他面板
            ['searchPanel', 'measureToolContainer', 'profileAnalysisPanel'].forEach(id => {
                const otherPanel = document.getElementById(id);
                if (otherPanel) {
                    otherPanel.style.display = 'none';
                    console.log(`已隐藏面板: ${id}`);
                }
            });
            
            // 显示当前面板
            panel.style.display = 'block';
            console.log('已设置display为block');
            
            // 尝试设置位置
            const toggleBtn = document.getElementById('toggleTerrainDig');
            if (toggleBtn) {
                try {
                    console.log('正在使用PanelPositioner设置面板位置');
                    console.log('PanelPositioner对象是否存在:', PanelPositioner ? true : false);
                    PanelPositioner.setPosition(toggleBtn, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                    console.log('面板位置设置完成');
                } catch (e) {
                    console.error('面板定位出错:', e);
                    // 默认位置
                    panel.style.position = 'fixed';
                    panel.style.top = '100px';
                    panel.style.left = '100px';
                    console.log('已使用默认位置');
                }
            } else {
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
                console.log('已使用默认位置（无PanelPositioner）');
            }
            
            // 延迟检查确认面板显示
            setTimeout(() => {
                if (window.getComputedStyle(panel).display !== 'block') {
                    console.warn('延迟检查: 面板可能未正确显示，强制显示');
                    panel.style.display = 'block';
                    panel.style.visibility = 'visible';
                }
            }, 100);
        } else {
            console.log('直接隐藏地形开挖面板');
            panel.style.display = 'none';
            console.log('已设置display为none');
        }
    } else {
        console.error('地形开挖面板元素不存在');
    }
};

// 导出类
window.TerrainDigHandler = TerrainDigHandler;
