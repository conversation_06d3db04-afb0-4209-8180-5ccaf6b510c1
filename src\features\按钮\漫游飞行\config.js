/**
 * roamFly模块配置文件
 */
export const RoamFlyConfig = {
    /**
     * 漫游配置
     */
    roam: {
        // 默认飞行高度（米）
        defaultHeight: 500,
        // 默认飞行速度（km/h）
        defaultSpeed: 1000,
        // 模型路径
        modelUrl: 'src/models/zhanji.glb',
        // 默认参数
        defaultParams: {
            viewMode: 'normal',   // 视角模式: normal, firstPerson, godView
            loop: true            // 循环播放
        },
        minHeight: 0,        // 最小高度 (米)
        maxHeight: 5000,     // 最大高度 (米)
        minSpeed: 100,       // 最小速度 (km/h)
        maxSpeed: 10000,      // 最大速度 (km/h)
    },
    
    /**
     * UI配置
     */
    ui: {
        // 按钮ID
        buttonId: 'roamFlyButton',
        // 按钮标题
        buttonTitle: '路线漫游',
        // 按钮图标
        buttonIcon: 'icon-roamfly',
        // 按钮顺序
        buttonOrder: 7,
        // 面板ID
        panelId: 'roamFlyPanel',
        // SVG图标定义
        svgIcon: `
            <symbol id="icon-roamfly" viewBox="0 0 24 24">
                <!-- 飞机机身 -->
                <ellipse cx="12" cy="12" rx="8" ry="2.5" fill="#4A90E2" stroke="#2171B5" stroke-width="0.5"/>
                
                <!-- 机翼 -->
                <ellipse cx="8" cy="12" rx="6" ry="1.2" fill="#7ED321" stroke="#5CB316" stroke-width="0.5"/>
                <ellipse cx="16" cy="12" rx="6" ry="1.2" fill="#7ED321" stroke="#5CB316" stroke-width="0.5"/>
                
                <!-- 机头 -->
                <circle cx="20" cy="12" r="1.5" fill="#F5A623" stroke="#E8930A" stroke-width="0.5"/>
                
                <!-- 尾翼 -->
                <path d="M4 12 L2 10 L3 12 L2 14 Z" fill="#4A90E2" stroke="#2171B5" stroke-width="0.3"/>
                
                <!-- 飞行轨迹 -->
                <path d="M1 8 Q6 6 12 8 T23 6" stroke="#F5A623" stroke-width="1.5" fill="none" stroke-dasharray="2,2" opacity="0.8"/>
                
                <!-- 推进器效果 -->
                <circle cx="4" cy="12" r="0.8" fill="#F5A623" opacity="0.6"/>
                <circle cx="4" cy="12" r="0.5" fill="white" opacity="0.8"/>
                
                <!-- 装饰细节 -->
                <line x1="12" y1="10.5" x2="12" y2="13.5" stroke="white" stroke-width="0.5" opacity="0.7"/>
                <circle cx="18" cy="12" r="0.3" fill="white" opacity="0.8"/>
            </symbol>
        `
    },
    
    // 视角模式
    viewModes: {
        NORMAL: 'normal',           // 普通视角
        FIRST_PERSON: 'firstPerson',// 第一人称
        GOD_VIEW: 'godView'        // 上帝视角
    },

    DEFAULT_SETTINGS: {
        height: 500, // 默认高度（米）
        speed: 1000, // 默认速度（km/h）
        heightFactor: 1.5, // 默认高度系数
        minHeight: 0, // 最小高度
        maxHeight: 2000, // 最大高度 
        minSpeed: 1, // 最小速度（km/h）
        maxSpeed: 10000, // 最大速度（km/h）
        minHeightFactor: 0, // 最小高度系数
        maxHeightFactor: 20, // 最大高度系数 
        cameraDistance: 200, // 默认视角距离（米）
        minCameraDistance: 10, // 最小视角距离（米）
        maxCameraDistance: 20000, // 最大视角距离（米）
        pitch: -30, // 默认俯仰角
        headingOffset: 0, // 默认朝向偏移
    }
}; 