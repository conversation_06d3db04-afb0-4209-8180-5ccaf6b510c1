/* 测量工具样式 */

#measureToolContainer {
    position: fixed !important;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 20px;
    min-width: 180px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(200, 200, 200, 0.3);
    z-index: 1000;
    color: #333;
}

#measureToolContainer .toolbar-panel-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    text-align: center;
}

#measureToolContainer .button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

#measureToolContainer .measure-btn {
    width: 100%;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    background: #f8f9fa;
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    text-align: left;
    height: 36px;
}

#measureToolContainer .measure-btn:hover {
    background: rgba(24, 144, 255, 0.1);
    border-color: rgba(24, 144, 255, 0.3);
    color: #1890ff;
    transform: translateX(2px);
}

#measureToolContainer .measure-btn.active {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
    transform: translateX(2px);
}

#measureToolContainer .measure-btn img {
    width: 16px;
    height: 16px;
}

#measureToolContainer .measure-btn.clear-btn {
    margin-top: 8px;
    background: #ff4d4f;
    border-color: #ff4d4f;
    color: white;
    justify-content: center;
}

#measureToolContainer .measure-btn.clear-btn:hover {
    background: #ff7875;
    border-color: #ff7875;
}

#measureToolContainer .measure-result {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

#measureToolContainer .measure-result .result-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

#measureToolContainer .measure-result .result-item:last-child {
    margin-bottom: 0;
}

#measureToolContainer .measure-result .label {
    color: #666;
}

#measureToolContainer .measure-result .value {
    color: #1890ff;
    font-weight: 500;
}

/* 退出测量按钮 */
.floating-exit-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    transition: all 0.3s ease;
    font-size: 13px;
    height: 36px;
}

.floating-exit-button:hover {
    background-color: #ff7875;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

.floating-exit-button img {
    width: 16px;
    height: 16px;
}

.measureTool {
    color: #333;
    background: rgba(255, 255, 255, 0.85);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(15px);
    width: 100%;
    box-sizing: border-box;
    border: 1px solid rgba(200, 200, 200, 0.3);
}

.measureTool .title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    text-align: center;
    color: #333;
}

.measureItem {
    padding: 8px 16px;
    margin: 6px 0;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 13px;
    text-align: center;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    height: 36px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    color: #333;
}

.measureItem:hover {
    background: rgba(24, 144, 255, 0.1);
    border-color: rgba(24, 144, 255, 0.3);
    color: #1890ff;
    transform: translateX(2px);
}

.measureItem img {
    width: 16px;
    height: 16px;
}

.measureItem:hover img {
    opacity: 1;
}

.selItembox1 {
    background: #1890ff !important;
    color: white !important;
    border-color: #1890ff !important;
    transform: translateX(2px);
}

.selItembox1 img {
    opacity: 1;
}

/* 测量结果样式 */
.measureResult {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.5;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.measureResult .label {
    color: #666;
    margin-right: 8px;
}

.measureResult .value {
    color: #1890ff;
    font-weight: 500;
}