/**
 * 打印模块配置文件
 */
const PrintConfig = {
    /**
     * 打印设置
     */
    print: {
        // 默认是否包含UI界面元素
        includeUI: false,
        // 默认图片格式
        imageFormat: 'image/png',
        // 默认图片质量 (0-1)
        imageQuality: 0.95,
        // 图片文件名
        defaultFilename: 'cesium-screenshot',
        // 打印区域选择器
        printContainerSelector: '#cesiumContainer',
    },

    /**
     * UI配置
     */
    ui: {
        // 按钮ID
        buttonId: 'printButton',
        // 按钮标题
        buttonTitle: '打印',
        // 按钮图标
        buttonIcon: 'icon-print',
        // 面板ID
        panelId: 'printPanel'
    },

    // 打印时要隐藏的元素类名
    hiddenElementsInPrint: [
        'cesium-viewer-toolbar',
        'cesium-viewer-fullscreenContainer',
        'cesium-widget-credits',
        'no-print'
    ]
}; 