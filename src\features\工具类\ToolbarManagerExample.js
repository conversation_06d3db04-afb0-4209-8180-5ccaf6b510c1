/**
 * 新工具栏管理器使用示例
 * 演示如何使用ButtonFactory和ToolbarManager优化现有的工具模块
 */

// 使用示例：将现有的MeasureUI模块迁移到新的管理器系统

/**
 * 原有的MeasureUI.init方法可以简化为：
 */
class MeasureUIOptimized {
    static async init(viewer, containerId) {
        // 确保ToolbarManager已初始化
        if (!window.toolbarManager) {
            window.toolbarManager = new ToolbarManager(viewer, containerId);
        }

        // 注册测量工具到工具栏
        const { toolInstance } = await window.toolbarManager.registerTool({
            id: 'measure',
            name: '测量工具',
            iconPath: 'src/features/工具/测量工具/svg/measure.svg',
            tooltip: '测量工具',
            
            // 工具创建函数
            createTool: async (viewer) => {
                return new MeasureTool({ viewer: viewer });
            },
            
            // 面板创建函数
            createPanel: async (viewer, toolInstance) => {
                return MeasureUIOptimized.createMeasurePanel(viewer, toolInstance);
            },
            
            // 可选配置
            options: {
                toggle: true, // 切换按钮
                position: 'append' // 添加到末尾
            }
        });

        console.log('✓ 测量工具注册完成');
        return toolInstance;
    }

    /**
     * 创建测量面板
     */
    static createMeasurePanel(viewer, measureTool) {
        const panel = document.createElement('div');
        panel.className = 'analysis-panel measure-panel';
        panel.innerHTML = `
            <div class="panel-title">测量工具</div>
            <div class="measure-buttons">
                <button data-measure="distance" class="measure-btn">
                    <img src="src/features/工具/测量工具/svg/distance.svg" alt="距离测量">
                    <span>距离</span>
                </button>
                <button data-measure="area" class="measure-btn">
                    <img src="src/features/工具/测量工具/svg/area.svg" alt="面积测量">
                    <span>面积</span>
                </button>
                <button data-measure="height" class="measure-btn">
                    <img src="src/features/工具/测量工具/svg/height.svg" alt="高度测量">
                    <span>高度</span>
                </button>
                <button data-measure="angle" class="measure-btn">
                    <img src="src/features/工具/测量工具/svg/angle.svg" alt="角度测量">
                    <span>角度</span>
                </button>
                <button data-action="clear" class="measure-btn clear-btn">
                    <img src="src/features/工具/测量工具/svg/clear.svg" alt="清除">
                    <span>清除</span>
                </button>
                <button data-action="close" class="measure-btn close-btn">
                    <img src="src/features/工具/测量工具/svg/close.svg" alt="关闭">
                    <span>关闭</span>
                </button>
            </div>
        `;

        // 绑定测量按钮事件
        panel.querySelectorAll('[data-measure]').forEach(btn => {
            btn.addEventListener('click', () => {
                const measureType = btn.dataset.measure;
                measureTool.startMeasure(measureType);
            });
        });

        // 绑定清除按钮事件
        panel.querySelector('[data-action="clear"]').addEventListener('click', () => {
            measureTool.clearAll();
        });

        document.body.appendChild(panel);
        return panel;
    }
}

/**
 * 批量迁移所有工具的示例
 */
async function initializeAllToolsWithNewManager(viewer) {
    // 创建全局工具栏管理器
    window.toolbarManager = new ToolbarManager(viewer, 'toolButtons');
    
    // 批量注册所有工具
    const toolConfigs = [
        {
            id: 'search',
            name: '搜索功能',
            iconPath: 'src/features/工具/搜索功能/svg/search.svg',
            tooltip: '搜索功能',
            createTool: (viewer) => new LocationSearch(viewer),
            createPanel: (viewer, tool) => SearchUIOptimized.createPanel(viewer, tool)
        },
        {
            id: 'terrain-dig',
            name: '地形开挖',
            iconPath: 'src/features/工具/地形开挖/svg/terrain-dig.svg',
            tooltip: '地形开挖',
            createTool: (viewer) => new TerrainDigHandler(viewer),
            createPanel: (viewer, tool) => TerrainDigUIOptimized.createPanel(viewer, tool)
        },
        {
            id: 'profile-analysis',
            name: '剖面分析',
            iconPath: 'src/features/工具/剖面分析/svg/profile-analysis.svg',
            tooltip: '剖面分析',
            createTool: (viewer) => new ProfileAnalysis(viewer),
            createPanel: (viewer, tool) => ProfileAnalysisUIOptimized.createPanel(viewer, tool)
        },
        {
            id: 'bookmark',
            name: '书签管理',
            iconPath: 'src/features/工具/书签管理/svg/bookmark.svg',
            tooltip: '书签管理',
            createTool: (viewer) => new BookmarkTool(viewer),
            createPanel: (viewer, tool) => BookmarkUIOptimized.createPanel(viewer, tool)
        }
        // ... 其他工具配置
    ];

    // 批量注册
    const results = await Promise.all(
        toolConfigs.map(config => window.toolbarManager.registerTool(config))
    );

    console.log(`✓ 成功注册 ${results.length} 个工具`);
    
    // 设置工具栏事件监听
    window.toolbarManager.on('tool:activated', (data) => {
        console.log(`工具激活: ${data.toolId}`);
    });
    
    window.toolbarManager.on('tool:deactivated', (data) => {
        console.log(`工具停用: ${data.toolId}`);
    });

    return window.toolbarManager;
}

/**
 * 在index.html中的使用方式：
 */
/*
// 在index.html的<script>标签中替换原有的初始化代码：

window.onload = async function() {
    try {
        // 初始化Cesium
        const viewer = initCesium();
        
        // 初始化坐标显示功能（保持不变）
        window.coordinateDisplay = new CoordinateDisplay(viewer);
        
        // 使用新的工具栏管理器初始化所有工具
        window.toolbarManager = await initializeAllToolsWithNewManager(viewer);
        
        // 可选：手动注册个别工具
        await window.toolbarManager.registerTool({
            id: 'custom-tool',
            name: '自定义工具',
            iconPath: 'path/to/icon.svg',
            tooltip: '自定义工具',
            createTool: (viewer) => new CustomTool(viewer),
            createPanel: (viewer, tool) => createCustomPanel(viewer, tool)
        });
        
        console.log('✓ 所有工具初始化完成');
        
    } catch (error) {
        console.error('初始化失败:', error);
    }
};
*/

/**
 * 优化效果对比：
 * 
 * 优化前（原有方式）：
 * - 每个UI类都有重复的按钮创建代码
 * - 事件处理逻辑分散
 * - 面板定位代码重复
 * - 按钮状态管理不统一
 * 
 * 优化后（新管理器）：
 * - 统一的按钮创建和事件处理
 * - 集中的状态管理
 * - 自动化的面板定位
 * - 标准化的工具生命周期
 * - 更好的错误处理和调试信息
 * - 支持批量操作和事件系统
 */

// 导出示例类
window.MeasureUIOptimized = MeasureUIOptimized;
window.initializeAllToolsWithNewManager = initializeAllToolsWithNewManager;