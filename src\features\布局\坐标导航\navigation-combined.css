/* 导航和坐标显示组件样式 - 合并版本 */

/* ------------------- 坐标显示样式 ------------------- */
.coordinate-display {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 10px;
    background: rgba(0, 0, 0, 0.2);
    padding: 3px 6px;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    z-index: 1001;
    border: none;
    box-shadow: none;
    text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.8), 0px 0px 2px rgba(0, 0, 0, 0.9);
}

.coordinate-content {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 2px;
}

.coordinate-text {
    display: flex;
    gap: 5px;
}

.coordinate-text span {
    min-width: 140px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    font-weight: 600;
}

/* 图标样式 */
.icon {
    display: inline-flex;
    width: 12px;
    height: 12px;
    margin-right: 2px;
    align-items: center;
    justify-content: center;
    filter: drop-shadow(0px 0px 1px rgba(0, 0, 0, 0.8));
}

.icon svg {
    width: 12px;
    height: 12px;
}

/* 修改SVG图标颜色 */
.icon svg path,
.icon svg line,
.icon svg circle,
.icon svg ellipse {
    stroke: white !important;
    stroke-width: 1.8 !important;
}

.coordinate-system {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1px 4px;
    border-radius: 6px;
    outline: none;
    cursor: pointer;
    font-size: 11px;
    font-weight: 600;
}

.coordinate-system option {
    background: #333;
    color: white;
    padding: 1px;
}

.coordinate-system:hover {
    background: rgba(0, 0, 0, 0.6);
}

.coordinate-system:focus {
    border-color: rgba(255, 255, 255, 0.4);
}

/* 添加比例尺样式 */
.scale-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 2px;
    margin-top: 2px;
    border-top: 1px solid rgba(255, 255, 255, 0.4);
}

.scale-icon-container {
    margin-bottom: 1px;
    display: flex;
    justify-content: center;
}

.scale-bar {
    height: 2px;
    background-color: white;
    width: 60px;
    margin-bottom: 1px;
    position: relative;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
}

.scale-bar:before, .scale-bar:after {
    content: '';
    position: absolute;
    height: 5px;
    width: 1px;
    background-color: white;
    top: -1.5px;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
}

.scale-bar:before {
    left: 0;
}

.scale-bar:after {
    right: 0;
}

.scale-text {
    font-size: 10px;
    color: white;
    text-align: center;
    font-weight: 600;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.8);
}

/* ------------------- 导航控件样式 ------------------- */
/* 隐藏原始距离图例 */
.distance-legend {
    display: none !important;
    pointer-events: auto;
    position: absolute;
    border-radius: 15px;
    padding-left: 5px;
    padding-right: 5px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 90px;
    height: 30px;
    width: 125px;
    box-sizing: content-box;
    background: rgba(0, 0, 0, 0.6);
}

.distance-legend-label {
    display: inline-block;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: lighter;
    line-height: 30px;
    color: #FFFFFF;
    width: 125px;
    text-align: center;
}

.distance-legend-scale-bar {
    border-left: 1px solid #FFFFFF;
    border-right: 1px solid #FFFFFF;
    border-bottom: 1px solid #FFFFFF;
    position: absolute;
    height: 10px;
    top: 15px;
}

.navigation-controls {
    position: absolute;
    right: 30px;
    top: 210px;
    width: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 300;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.navigation-control {
    cursor: pointer;
    border-bottom: 1px solid #555555;
}

.naviagation-control:active {
    color: #FFF;
}

.navigation-control-last {
    cursor: pointer;
    border-bottom: 0;
}

.navigation-control-icon-zoom-in {
    position: relative;
    text-align: center;
    font-size: 20px;
    color: #FFFFFF;
    padding-bottom: 4px;
}

.navigation-control-icon-zoom-out {
    position: relative;
    text-align: center;
    font-size: 20px;
    color: #FFFFFF;
}

.navigation-control-icon-reset {
    position: relative;
    left: 10px;
    width: 10px;
    height: 10px;
    fill: rgba(255, 255, 255, 0.8);
    padding-top: 6px;
    padding-bottom: 6px;
    box-sizing: content-box;
}

.compass {
    pointer-events: auto;
    position: absolute;
    right: 0px;
    top: 100px;
    width: 95px;
    height: 95px;
    overflow: hidden;
}

.compass-outer-ring {
    position: absolute;
    top: 0;
    width: 95px;
    height: 95px;
    fill: rgba(255, 255, 255, 0.5);
}

.compass-outer-ring-background {
    position: absolute;
    top: 14px;
    left: 14px;
    width: 44px;
    height: 44px;
    border-radius: 44px;
    border: 12px solid rgba(47, 53, 60, 0.8);
    box-sizing: content-box;
}

.compass-gyro {
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 95px;
    height: 95px;
    fill: #CCC;
}

.compass-gyro-active {
    fill: #68ADFE;
}

.compass-gyro-background {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 33px;
    height: 33px;
    border-radius: 33px;
    background-color: rgba(47, 53, 60, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-sizing: content-box;
}

.compass-gyro-background:hover + .compass-gyro {
    fill: #68ADFE;
}

.compass-rotation-marker {
    position: absolute;
    top: 0;
    width: 95px;
    height: 95px;
    fill: #68ADFE;
}

/* ------------------- 媒体查询 ------------------- */
@media print {
    .coordinate-display,
    .distance-legend,
    .navigation-controls,
    .compass {
        display: none;
    }
}

@media screen and (max-width: 700px),
screen and (max-height: 420px) {
    .coordinate-display,
    .distance-legend,
    .navigation-controls,
    .compass {
        display: none;
    }
} 