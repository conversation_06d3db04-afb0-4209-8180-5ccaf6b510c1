/* 搜索面板样式 */

#searchPanel {
    position: fixed !important;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    min-width: 300px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(200, 200, 200, 0.3);
    z-index: 1000;
    color: #333;
    display: none; /* 默认隐藏 */
}

#searchPanel .toolbar-panel-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    text-align: center;
}

#searchPanel .input-group {
    margin: 10px 0;
    position: relative;
}

#searchPanel input {
    width: calc(100% - 90px);
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    color: #333;
    font-size: 13px;
    transition: all 0.3s ease;
    height: 36px;
}

#searchPanel input:focus {
    background: #fff;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    outline: none;
}

#searchPanel input::placeholder {
    color: #999;
}

#searchPanel .button-group {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 8px;
}

#searchPanel button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    background: #1890ff;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 36px;
}

#searchPanel button:hover {
    background: #40a9ff;
    transform: translateY(-1px);
}

#searchPanel button.stop-btn {
    background: #ff4d4f;
}

#searchPanel button.stop-btn:hover {
    background: #ff7875;
}

#searchPanel .search-tip {
    margin-top: 16px;
    text-align: center;
    font-size: 13px;
    color: #666;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    line-height: 1.5;
} 