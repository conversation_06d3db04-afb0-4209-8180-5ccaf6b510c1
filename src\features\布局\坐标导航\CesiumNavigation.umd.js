// @ts-nocheck
// 导航控制组件
/*! cesium-navigation-es6 v3.0.8 - CesiumNavigation.umd.js, 0a5cdc118ae6ce394b01, Fri May 20 2022 13:59:10 GMT+0800 (中国标准时间) */
!function(e, t) {
    "object" == typeof exports && "object" == typeof module ? module.exports = t(require("cesium")) : "function" == typeof define && define.amd ? define(["Cesium"], t) : "object" == typeof exports ? exports.CesiumNavigation = t(require("Cesium")) : e.CesiumNavigation = t(e.Cesium)
}(self, (function(e) {
    return ( () => {
        var t = {
            705: e => {
                "use strict";
                e.exports = function(e) {
                    var t = [];
                    return t.toString = function() {
                        return this.map((function(t) {
                            var i = ""
                              , n = void 0 !== t[5];
                            return t[4] && (i += "@supports (".concat(t[4], ") {")),
                            t[2] && (i += "@media ".concat(t[2], " {")),
                            n && (i += "@layer".concat(t[5].length > 0 ? " ".concat(t[5]) : "", " {")),
                            i += e(t),
                            n && (i += "}"),
                            t[2] && (i += "}"),
                            t[4] && (i += "}"),
                            i
                        }
                        )).join("")
                    }
                    ,
                    t.i = function(e, i, n, o, r) {
                        "string" == typeof e && (e = [[null, e, void 0]]);
                        var a = {};
                        if (n)
                            for (var s = 0; s < this.length; s++) {
                                var d = this[s][0];
                                null != d && (a[d] = !0)
                            }
                        for (var c = 0; c < e.length; c++) {
                            var l = [].concat(e[c]);
                            n && a[l[0]] || (void 0 !== r && (void 0 === l[5] || (l[1] = "@layer".concat(l[5].length > 0 ? " ".concat(l[5]) : "", " {").concat(l[1], "}")),
                            l[5] = r),
                            i && (l[2] ? (l[1] = "@media ".concat(l[2], " {").concat(l[1], "}"),
                            l[2] = i) : l[2] = i),
                            o && (l[4] ? (l[1] = "@supports (".concat(l[4], ") {").concat(l[1], "}"),
                            l[4] = o) : l[4] = "".concat(o)),
                            t.push(l))
                        }
                    }
                    ,
                    t
                }
            }
            ,
            738: e => {
                "use strict";
                e.exports = function(e) {
                    return e[1]
                }
            }
            ,
            314: (e, t, i) => {
                "use strict";
                i.r(t),
                i.d(t, {
                    default: () => s
                });
                var n = i(738)
                  , o = i.n(n)
                  , r = i(705)
                  , a = i.n(r)()(o());
                a.push([e.id, ".distance-legend {\r\n  pointer-events: auto;\r\n  position: absolute;\r\n  border-radius: 15px;\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  bottom: 30px;\r\n  height: 30px;\r\n  width: 125px;\r\n  box-sizing: content-box;\r\n}\r\n\r\n.distance-legend-label {\r\n  display: inline-block;\r\n  font-family: 'Roboto', sans-serif;\r\n  font-size: 14px;\r\n  font-weight: lighter;\r\n  line-height: 30px;\r\n  color: #FFFFFF;\r\n  width: 125px;\r\n  text-align: center;\r\n}\r\n\r\n.distance-legend-scale-bar {\r\n  border-left: 1px solid #FFFFFF;\r\n  border-right: 1px solid #FFFFFF;\r\n  border-bottom: 1px solid #FFFFFF;\r\n  position: absolute;\r\n  height: 10px;\r\n  top: 15px;\r\n}\r\n\r\n@media print {\r\n  .distance-legend {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 700px),\r\nscreen and (max-height: 420px) {\r\n  .distance-legend {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.navigation-controls {\r\n  position: absolute;\r\n  right: 30px;\r\n  top: 210px;\r\n  width: 30px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  font-weight: 300;\r\n  -webkit-touch-callout: none;\r\n  -webkit-user-select: none;\r\n  -khtml-user-select: none;\r\n  -moz-user-select: none;\r\n  -ms-user-select: none;\r\n  user-select: none;\r\n}\r\n\r\n.navigation-control {\r\n  cursor: pointer;\r\n  border-bottom: 1px solid #555555;\r\n}\r\n\r\n.naviagation-control:active {\r\n  color: #FFF;\r\n}\r\n\r\n.navigation-control-last {\r\n  cursor: pointer;\r\n  border-bottom: 1px solid #555555;\r\n  border-bottom: 0;\r\n}\r\n\r\n.navigation-control-icon-zoom-in {\r\n  position: relative;\r\n  text-align: center;\r\n  font-size: 20px;\r\n  color: #FFFFFF;\r\n  padding-bottom: 4px;\r\n}\r\n\r\n.navigation-control-icon-zoom-out {\r\n  position: relative;\r\n  text-align: center;\r\n  font-size: 20px;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.navigation-control-icon-reset {\r\n  position: relative;\r\n  left: 10px;\r\n  width: 10px;\r\n  height: 10px;\r\n  fill: rgba(255, 255, 255, 0.8);\r\n  padding-top: 6px;\r\n  padding-bottom: 6px;\r\n  box-sizing: content-box;\r\n}\r\n\r\n.compass {\r\n  pointer-events: auto;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 100px;\r\n  width: 95px;\r\n  height: 95px;\r\n  overflow: hidden;\r\n}\r\n\r\n.compass-outer-ring {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 95px;\r\n  height: 95px;\r\n  fill: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.compass-outer-ring-background {\r\n  position: absolute;\r\n  top: 14px;\r\n  left: 14px;\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 44px;\r\n  border: 12px solid rgba(47, 53, 60, 0.8);\r\n  box-sizing: content-box;\r\n}\r\n\r\n.compass-gyro {\r\n  pointer-events: none;\r\n  position: absolute;\r\n  top: 0;\r\n  width: 95px;\r\n  height: 95px;\r\n  fill: #CCC;\r\n}\r\n\r\n.compass-gyro-active {\r\n  fill: #68ADFE;\r\n}\r\n\r\n.compass-gyro-background {\r\n  position: absolute;\r\n  top: 30px;\r\n  left: 30px;\r\n  width: 33px;\r\n  height: 33px;\r\n  border-radius: 33px;\r\n  background-color: rgba(47, 53, 60, 0.8);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  box-sizing: content-box;\r\n}\r\n\r\n.compass-gyro-background:hover+.compass-gyro {\r\n  fill: #68ADFE;\r\n}\r\n\r\n.compass-rotation-marker {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 95px;\r\n  height: 95px;\r\n  fill: #68ADFE;\r\n}\r\n\r\n@media screen and (max-width: 700px),\r\nscreen and (max-height: 420px) {\r\n  .navigation-controls {\r\n    display: none;\r\n  }\r\n\r\n  .compass {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media print {\r\n  .navigation-controls {\r\n    display: none;\r\n  }\r\n\r\n  .compass {\r\n    display: none;\r\n  }\r\n}", ""]);
                const s = a
            }
            ,
            984: (e, t, i) => {
                var n = i(379)
                  , o = i(314);
                "string" == typeof (o = o.__esModule ? o.default : o) && (o = [[e.id, o, ""]]);
                var r = {
                    insert: "head",
                    singleton: !1
                };
                n(o, r);
                e.exports = o.locals || {}
            }
            ,
            379: (e, t, i) => {
                "use strict";
                var n, o = function() {
                    return void 0 === n && (n = Boolean(window && document && document.all && !window.atob)),
                    n
                }, r = function() {
                    var e = {};
                    return function(t) {
                        if (void 0 === e[t]) {
                            var i = document.querySelector(t);
                            if (window.HTMLIFrameElement && i instanceof window.HTMLIFrameElement)
                                try {
                                    i = i.contentDocument.head
                                } catch (e) {
                                    i = null
                                }
                            e[t] = i
                        }
                        return e[t]
                    }
                }(), a = [];
                function s(e) {
                    for (var t = -1, i = 0; i < a.length; i++)
                        if (a[i].identifier === e) {
                            t = i;
                            break
                        }
                    return t
                }
                function d(e, t) {
                    for (var i = {}, n = [], o = 0; o < e.length; o++) {
                        var r = e[o]
                          , d = t.base ? r[0] + t.base : r[0]
                          , c = i[d] || 0
                          , l = "".concat(d, " ").concat(c);
                        i[d] = c + 1;
                        var p = s(l)
                          , u = {
                            css: r[1],
                            media: r[2],
                            sourceMap: r[3]
                        };
                        -1 !== p ? (a[p].references++,
                        a[p].updater(u)) : a.push({
                            identifier: l,
                            updater: g(u, t),
                            references: 1
                        }),
                        n.push(l)
                    }
                    return n
                }
                function c(e) {
                    var t = document.createElement("style")
                      , n = e.attributes || {};
                    if (void 0 === n.nonce) {
                        var o = i.nc;
                        o && (n.nonce = o)
                    }
                    if (Object.keys(n).forEach((function(e) {
                        t.setAttribute(e, n[e])
                    }
                    )),
                    "function" == typeof e.insert)
                        e.insert(t);
                    else {
                        var a = r(e.insert || "head");
                        if (!a)
                            throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");
                        a.appendChild(t)
                    }
                    return t
                }
                var l, p = (l = [],
                function(e, t) {
                    return l[e] = t,
                    l.filter(Boolean).join("\n")
                }
                );
                function u(e, t, i, n) {
                    var o = i ? "" : n.media ? "@media ".concat(n.media, " {").concat(n.css, "}") : n.css;
                    if (e.styleSheet)
                        e.styleSheet.cssText = p(t, o);
                    else {
                        var r = document.createTextNode(o)
                          , a = e.childNodes;
                        a[t] && e.removeChild(a[t]),
                        a.length ? e.insertBefore(r, a[t]) : e.appendChild(r)
                    }
                }
                function h(e, t, i) {
                    var n = i.css
                      , o = i.media
                      , r = i.sourceMap;
                    if (o ? e.setAttribute("media", o) : e.removeAttribute("media"),
                    r && "undefined" != typeof btoa && (n += "\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r)))), " */")),
                    e.styleSheet)
                        e.styleSheet.cssText = n;
                    else {
                        for (; e.firstChild; )
                            e.removeChild(e.firstChild);
                        e.appendChild(document.createTextNode(n))
                    }
                }
                var v = null
                  , m = 0;
                function g(e, t) {
                    var i, n, o;
                    if (t.singleton) {
                        var r = m++;
                        i = v || (v = c(t)),
                        n = u.bind(null, i, r, !1),
                        o = u.bind(null, i, r, !0)
                    } else
                        i = c(t),
                        n = h.bind(null, i, t),
                        o = function() {
                            !function(e) {
                                if (null === e.parentNode)
                                    return !1;
                                e.parentNode.removeChild(e)
                            }(i)
                        }
                        ;
                    return n(e),
                    function(t) {
                        if (t) {
                            if (t.css === e.css && t.media === e.media && t.sourceMap === e.sourceMap)
                                return;
                            n(e = t)
                        } else
                            o()
                    }
                }
                e.exports = function(e, t) {
                    (t = t || {}).singleton || "boolean" == typeof t.singleton || (t.singleton = o());
                    var i = d(e = e || [], t);
                    return function(e) {
                        if (e = e || [],
                        "[object Array]" === Object.prototype.toString.call(e)) {
                            for (var n = 0; n < i.length; n++) {
                                var o = s(i[n]);
                                a[o].references--
                            }
                            for (var r = d(e, t), c = 0; c < i.length; c++) {
                                var l = s(i[c]);
                                0 === a[l].references && (a[l].updater(),
                                a.splice(l, 1))
                            }
                            i = r
                        }
                    }
                }
            }
            ,
            89: t => {
                "use strict";
                t.exports = e
            }
        }
          , i = {};
        function n(e) {
            var o = i[e];
            if (void 0 !== o)
                return o.exports;
            var r = i[e] = {
                id: e,
                exports: {}
            };
            return t[e](r, r.exports, n),
            r.exports
        }
        n.n = e => {
            var t = e && e.__esModule ? () => e.default : () => e;
            return n.d(t, {
                a: t
            }),
            t
        }
        ,
        n.d = (e, t) => {
            for (var i in t)
                n.o(t, i) && !n.o(e, i) && Object.defineProperty(e, i, {
                    enumerable: !0,
                    get: t[i]
                })
        }
        ,
        n.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t),
        n.r = e => {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
                value: "Module"
            }),
            Object.defineProperty(e, "__esModule", {
                value: !0
            })
        }
        ;
        var o = {};
        return ( () => {
            "use strict";
            n.d(o, {
                default: () => W
            });
            n(984);
            var e = n(89);
            const t = function(e) {
                var t = document.createElement("div");
                t.innerHTML = e;
                for (var i = document.createDocumentFragment(); t.firstChild; )
                    i.appendChild(t.firstChild);
                return i
            };
            var i = e.knockout;
            const r = function(n, o, r) {
                o = (0,
                e.getElement)(o);
                var a, s = t(n), d = [];
                for (a = 0; a < s.childNodes.length; ++a)
                    d.push(s.childNodes[a]);
                for (o.appendChild(s),
                a = 0; a < d.length; ++a) {
                    var c = d[a];
                    1 !== c.nodeType && 8 !== c.nodeType || i.applyBindings(r, c)
                }
                return d
            };
            var a = e.knockout
              , s = function(t) {
                if (!(0,
                e.defined)(t) || !(0,
                e.defined)(t.terria))
                    throw new e.DeveloperError("options.terria is required.");
                this.terria = t.terria,
                this._removeSubscription = void 0,
                this._lastLegendUpdate = void 0,
                this.eventHelper = new e.EventHelper,
                this.distanceLabel = void 0,
                this.barWidth = void 0,
                this.enableDistanceLegend = !(0,
                e.defined)(t.enableDistanceLegend) || t.enableDistanceLegend,
                a.track(this, ["distanceLabel", "barWidth"]),
                this.eventHelper.add(this.terria.afterWidgetChanged, (function() {
                    (0,
                    e.defined)(this._removeSubscription) && (this._removeSubscription(),
                    this._removeSubscription = void 0)
                }
                ), this);
                var i = this;
                function n() {
                    if ((0,
                    e.defined)(i.terria)) {
                        var t = i.terria.scene;
                        i._removeSubscription = t.postRender.addEventListener((function() {
                            !function(t, i) {
                                if (!t.enableDistanceLegend)
                                    return t.barWidth = void 0,
                                    void (t.distanceLabel = void 0);
                                var n = (0,
                                e.getTimestamp)();
                                if (n < t._lastLegendUpdate + 250)
                                    return;
                                t._lastLegendUpdate = n;
                                var o = i.canvas.clientWidth
                                  , r = i.canvas.clientHeight
                                  , a = i.camera.getPickRay(new e.Cartesian2(o / 2 | 0,r - 1))
                                  , s = i.camera.getPickRay(new e.Cartesian2(1 + o / 2 | 0,r - 1))
                                  , l = i.globe
                                  , p = l.pick(a, i)
                                  , u = l.pick(s, i);
                                if (!(0,
                                e.defined)(p) || !(0,
                                e.defined)(u))
                                    return t.barWidth = void 0,
                                    void (t.distanceLabel = void 0);
                                var h = l.ellipsoid.cartesianToCartographic(p)
                                  , v = l.ellipsoid.cartesianToCartographic(u);
                                d.setEndPoints(h, v);
                                for (var m, g = d.surfaceDistance, f = 100, b = c.length - 1; !(0,
                                e.defined)(m) && b >= 0; --b)
                                    c[b] / g < f && (m = c[b]);
                                if ((0,
                                e.defined)(m)) {
                                    var C;
                                    C = m >= 1e3 ? (m / 1e3).toString() + " km" : m.toString() + " m",
                                    t.barWidth = m / g | 0,
                                    t.distanceLabel = C
                                } else
                                    t.barWidth = void 0,
                                    t.distanceLabel = void 0
                            }(this, t)
                        }
                        ), i)
                    }
                }
                n(),
                this.eventHelper.add(this.terria.afterWidgetChanged, (function() {
                    n()
                }
                ), this)
            };
            s.prototype.destroy = function() {
                this.eventHelper.removeAll()
            }
            ,
            s.prototype.show = function(e) {
                var t;
                t = this.enableDistanceLegend ? '<div class="distance-legend" data-bind="visible: distanceLabel && barWidth"><div class="distance-legend-label" data-bind="text: distanceLabel"></div><div class="distance-legend-scale-bar" data-bind="style: { width: barWidth + \'px\', left: (5 + (125 - barWidth) / 2) + \'px\' }"></div></div>' : '<div class="distance-legend"  style="display: none;" data-bind="visible: distanceLabel && barWidth"><div class="distance-legend-label"  data-bind="text: distanceLabel"></div><div class="distance-legend-scale-bar"  data-bind="style: { width: barWidth + \'px\', left: (5 + (125 - barWidth) / 2) + \'px\' }"></div></div>',
                r(t, e, this)
            }
            ,
            s.create = function(e) {
                var t = new s(e);
                return t.show(e.container),
                t
            }
            ;
            var d = new e.EllipsoidGeodesic
              , c = [1, 2, 3, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1e3, 2e3, 3e3, 5e3, 1e4, 2e4, 3e4, 5e4, 1e5, 2e5, 3e5, 5e5, 1e6, 2e6, 3e6, 5e6, 1e7, 2e7, 3e7, 5e7];
            const l = s;
            const p = "M 7.5,0 C 3.375,0 0,3.375 0,7.5 0,11.625 3.375,15 7.5,15 c 3.46875,0 6.375,-2.4375 7.21875,-5.625 l -1.96875,0 C 12,11.53125 9.9375,13.125 7.5,13.125 4.40625,13.125 1.875,10.59375 1.875,7.5 1.875,4.40625 4.40625,1.875 7.5,1.875 c 1.59375,0 2.90625,0.65625 3.9375,1.6875 l -3,3 6.5625,0 L 15,0 12.75,2.25 C 11.4375,0.84375 9.5625,0 7.5,0 z";
            var u = e.knockout
              , h = function(t) {
                if (!(0,
                e.defined)(t))
                    throw new e.DeveloperError("terria is required");
                this._terria = t,
                this.name = "Unnamed Control",
                this.text = void 0,
                this.svgIcon = void 0,
                this.svgHeight = void 0,
                this.svgWidth = void 0,
                this.cssClass = void 0,
                this.isActive = !1,
                u.track(this, ["name", "svgIcon", "svgHeight", "svgWidth", "cssClass", "isActive"])
            };
            Object.defineProperties(h.prototype, {
                terria: {
                    get: function() {
                        return this._terria
                    }
                },
                hasText: {
                    get: function() {
                        return (0,
                        e.defined)(this.text) && "string" == typeof this.text
                    }
                }
            }),
            h.prototype.activate = function() {
                throw new e.DeveloperError("activate must be implemented in the derived class.")
            }
            ;
            const v = h;
            var m = function(e) {
                v.apply(this, arguments)
            };
            m.prototype = Object.create(v.prototype);
            const g = m;
            var f = function(e) {
                g.apply(this, arguments),
                this.name = e.options.resetTooltip ? e.options.resetTooltip : "重置视图",
                this.navigationLocked = !1,
                this.svgIcon = e.options.resetSvg ? "" : p,
                this.resetSvg = e.options.resetSvg,
                this.svgHeight = 15,
                this.svgWidth = 15,
                this.cssClass = "navigation-control-icon-reset"
            };
            (f.prototype = Object.create(g.prototype)).setNavigationLocked = function(e) {
                this.navigationLocked = e
            }
            ,
            f.prototype.resetView = function() {
                if (!this.navigationLocked) {
                    var t = this.terria.scene;
                    if (t.screenSpaceCameraController.enableInputs) {
                        this.isActive = !0;
                        var i = t.camera;
                        if ((0,e.defined)(this.terria.trackedEntity)) {
                            var n = this.terria.trackedEntity;
                            this.terria.trackedEntity = void 0,
                            this.terria.trackedEntity = n
                        } else {
                            const n = this.terria.options.duration ? this.terria.options.duration : 3;
                            i.flyTo({
                                destination: e.Cartesian3.fromDegrees(103.0, 36.0, 12000000),
                                orientation: {
                                    heading: 0.0,
                                    pitch: -e.Math.PI_OVER_TWO,
                                    roll: 0.0
                                },
                                duration: n
                            });
                        }
                        this.isActive = !1
                    }
                }
            }
            ,
            f.prototype.activate = function() {
                this.resetView()
            }
            ;
            const b = f;
            var C = {}
              , x = new e.Cartographic
              , y = new e.Ray;
            C.getCameraFocus = function(t, i, n) {
                var o = t.scene
                  , r = o.camera;
                if (o.mode !== e.SceneMode.MORPHING && ((0,
                e.defined)(n) || (n = new e.Cartesian3),
                (0,
                e.defined)(t.trackedEntity) ? n = t.trackedEntity.position.getValue(t.clock.currentTime, n) : (y.origin = r.positionWC,
                y.direction = r.directionWC,
                n = o.globe.pick(y, o, n)),
                (0,
                e.defined)(n)))
                    return o.mode === e.SceneMode.SCENE2D || o.mode === e.SceneMode.COLUMBUS_VIEW ? (n = r.worldToCameraCoordinatesPoint(n, n),
                    i && (n = o.globe.ellipsoid.cartographicToCartesian(o.mapProjection.unproject(n, x), n))) : i || (n = r.worldToCameraCoordinatesPoint(n, n)),
                    n
            }
            ;
            const M = C;
            var k = function(e, t) {
                g.apply(this, arguments),
                this.name = t ? e.options.zoomInTooltip ? e.options.zoomInTooltip : "放大" : e.options.zoomOutTooltip ? e.options.zoomOutTooltip : "缩小",
                t ? e.options.zoomInSvg ? this.zoomInSvg = e.options.zoomInSvg : this.text = "+" : e.options.zoomOutSvg ? this.zoomOutSvg = e.options.zoomOutSvg : this.text = "-",
                this.cssClass = "navigation-control-icon-zoom-" + (t ? "in" : "out"),
                this.relativeAmount = 2,
                t && (this.relativeAmount = 1 / this.relativeAmount)
            };
            k.prototype.relativeAmount = 1,
            (k.prototype = Object.create(g.prototype)).activate = function() {
                this.zoom(this.relativeAmount)
            }
            ;
            var w = new e.Cartesian3;
            k.prototype.zoom = function(t) {
                if (this.isActive = !0,
                (0,
                e.defined)(this.terria)) {
                    var i = this.terria.scene
                      , n = i.screenSpaceCameraController;
                    if (!n.enableInputs || !n.enableZoom)
                        return;
                    var o, r = i.camera;
                    switch (i.mode) {
                    case e.SceneMode.MORPHING:
                        break;
                    case e.SceneMode.SCENE2D:
                        r.zoomIn(r.positionCartographic.height * (1 - this.relativeAmount));
                        break;
                    default:
                        var a;
                        if (a = (0,
                        e.defined)(this.terria.trackedEntity) ? new e.Cartesian3 : M.getCameraFocus(this.terria, !1),
                        (0,
                        e.defined)(a))
                            o = {
                                direction: r.direction,
                                up: r.up
                            };
                        else {
                            var s = new e.Ray(r.worldToCameraCoordinatesPoint(i.globe.ellipsoid.cartographicToCartesian(r.positionCartographic)),r.directionWC);
                            a = e.IntersectionTests.grazingAltitudeLocation(s, i.globe.ellipsoid),
                            o = {
                                heading: r.heading,
                                pitch: r.pitch,
                                roll: r.roll
                            }
                        }
                        if (r.position.z >= 406944828719368.56)
                            return;
                        var d = e.Cartesian3.subtract(r.position, a, w)
                          , c = e.Cartesian3.multiplyByScalar(d, t, d)
                          , l = e.Cartesian3.add(a, c, a);
                        (0,
                        e.defined)(this.terria.trackedEntity) || i.mode === e.SceneMode.COLUMBUS_VIEW ? r.position = l : r.flyTo({
                            destination: l,
                            orientation: o,
                            duration: .5,
                            convert: !1
                        })
                    }
                }
                this.isActive = !1
            }
            ;
            const F = k;
            var L = e.knockout
              , S = function(t) {
                this.terria = t.terria,
                this.eventHelper = new e.EventHelper,
                this.enableZoomControls = !(0,
                e.defined)(t.enableZoomControls) || t.enableZoomControls,
                this.enableCompass = !(0,
                e.defined)(t.enableCompass) || t.enableCompass,
                this.navigationLocked = !1,
                this.controls = t.controls,
                (0,
                e.defined)(this.controls) || (this.controls = [new F(this.terria,!0), new b(this.terria), new F(this.terria,!1)]),
                this.svgCompassOuterRing = "m 66.5625,0 0,15.15625 3.71875,0 0,-10.40625 5.5,10.40625 4.375,0 0,-15.15625 -3.71875,0 0,10.40625 L 70.9375,0 66.5625,0 z M 72.5,20.21875 c -28.867432,0 -52.28125,23.407738 -52.28125,52.28125 0,28.87351 23.413818,52.3125 52.28125,52.3125 28.86743,0 52.28125,-23.43899 52.28125,-52.3125 0,-28.873512 -23.41382,-52.28125 -52.28125,-52.28125 z m 0,1.75 c 13.842515,0 26.368948,5.558092 35.5,14.5625 l -11.03125,11 0.625,0.625 11.03125,-11 c 8.9199,9.108762 14.4375,21.579143 14.4375,35.34375 0,13.764606 -5.5176,26.22729 -14.4375,35.34375 l -11.03125,-11 -0.625,0.625 11.03125,11 c -9.130866,9.01087 -21.658601,14.59375 -35.5,14.59375 -13.801622,0 -26.321058,-5.53481 -35.4375,-14.5 l 11.125,-11.09375 c 6.277989,6.12179 14.857796,9.90625 24.3125,9.90625 19.241896,0 34.875,-15.629154 34.875,-34.875 0,-19.245847 -15.633104,-34.84375 -34.875,-34.84375 -9.454704,0 -18.034511,3.760884 -24.3125,9.875 L 37.0625,36.4375 C 46.179178,27.478444 58.696991,21.96875 72.5,21.96875 z m -0.875,0.84375 0,13.9375 1.75,0 0,-13.9375 -1.75,0 z M 36.46875,37.0625 47.5625,48.15625 C 41.429794,54.436565 37.65625,63.027539 37.65625,72.5 c 0,9.472461 3.773544,18.055746 9.90625,24.34375 L 36.46875,107.9375 c -8.96721,-9.1247 -14.5,-21.624886 -14.5,-35.4375 0,-13.812615 5.53279,-26.320526 14.5,-35.4375 z M 72.5,39.40625 c 18.297686,0 33.125,14.791695 33.125,33.09375 0,18.302054 -14.827314,33.125 -33.125,33.125 -18.297687,0 -33.09375,-14.822946 -33.09375,-33.125 0,-18.302056 14.796063,-33.09375 33.09375,-33.09375 z M 22.84375,71.625 l 0,1.75 13.96875,0 0,-1.75 -13.96875,0 z m 85.5625,0 0,1.75 14,0 0,-1.75 -14,0 z M 71.75,108.25 l 0,13.9375 1.71875,0 0,-13.9375 -1.71875,0 z",
                this.svgCompassGyro = "m 72.71875,54.375 c -0.476702,0 -0.908208,0.245402 -1.21875,0.5625 -0.310542,0.317098 -0.551189,0.701933 -0.78125,1.1875 -0.172018,0.363062 -0.319101,0.791709 -0.46875,1.25 -6.91615,1.075544 -12.313231,6.656514 -13,13.625 -0.327516,0.117495 -0.661877,0.244642 -0.9375,0.375 -0.485434,0.22959 -0.901634,0.471239 -1.21875,0.78125 -0.317116,0.310011 -0.5625,0.742111 -0.5625,1.21875 l 0.03125,0 c 0,0.476639 0.245384,0.877489 0.5625,1.1875 0.317116,0.310011 0.702066,0.58291 1.1875,0.8125 0.35554,0.168155 0.771616,0.32165 1.21875,0.46875 1.370803,6.10004 6.420817,10.834127 12.71875,11.8125 0.146999,0.447079 0.30025,0.863113 0.46875,1.21875 0.230061,0.485567 0.470708,0.870402 0.78125,1.1875 0.310542,0.317098 0.742048,0.5625 1.21875,0.5625 0.476702,0 0.876958,-0.245402 1.1875,-0.5625 0.310542,-0.317098 0.582439,-0.701933 0.8125,-1.1875 0.172018,-0.363062 0.319101,-0.791709 0.46875,-1.25 6.249045,-1.017063 11.256351,-5.7184 12.625,-11.78125 0.447134,-0.1471 0.86321,-0.300595 1.21875,-0.46875 0.485434,-0.22959 0.901633,-0.502489 1.21875,-0.8125 0.317117,-0.310011 0.5625,-0.710861 0.5625,-1.1875 l -0.03125,0 c 0,-0.476639 -0.245383,-0.908739 -0.5625,-1.21875 C 89.901633,71.846239 89.516684,71.60459 89.03125,71.375 88.755626,71.244642 88.456123,71.117495 88.125,71 87.439949,64.078341 82.072807,58.503735 75.21875,57.375 c -0.15044,-0.461669 -0.326927,-0.884711 -0.5,-1.25 -0.230061,-0.485567 -0.501958,-0.870402 -0.8125,-1.1875 -0.310542,-0.317098 -0.710798,-0.5625 -1.1875,-0.5625 z m -0.0625,1.40625 c 0.03595,-0.01283 0.05968,0 0.0625,0 0.0056,0 0.04321,-0.02233 0.1875,0.125 0.144288,0.147334 0.34336,0.447188 0.53125,0.84375 0.06385,0.134761 0.123901,0.309578 0.1875,0.46875 -0.320353,-0.01957 -0.643524,-0.0625 -0.96875,-0.0625 -0.289073,0 -0.558569,0.04702 -0.84375,0.0625 C 71.8761,57.059578 71.936151,56.884761 72,56.75 c 0.18789,-0.396562 0.355712,-0.696416 0.5,-0.84375 0.07214,-0.07367 0.120304,-0.112167 0.15625,-0.125 z m 0,2.40625 c 0.448007,0 0.906196,0.05436 1.34375,0.09375 0.177011,0.592256 0.347655,1.271044 0.5,2.03125 0.475097,2.370753 0.807525,5.463852 0.9375,8.9375 -0.906869,-0.02852 -1.834463,-0.0625 -2.78125,-0.0625 -0.92298,0 -1.802327,0.03537 -2.6875,0.0625 0.138529,-3.473648 0.493653,-6.566747 0.96875,-8.9375 0.154684,-0.771878 0.320019,-1.463985 0.5,-2.0625 0.405568,-0.03377 0.804291,-0.0625 1.21875,-0.0625 z m -2.71875,0.28125 c -0.129732,0.498888 -0.259782,0.987558 -0.375,1.5625 -0.498513,2.487595 -0.838088,5.693299 -0.96875,9.25 -3.21363,0.15162 -6.119596,0.480068 -8.40625,0.9375 -0.682394,0.136509 -1.275579,0.279657 -1.84375,0.4375 0.799068,-6.135482 5.504716,-11.036454 11.59375,-12.1875 z M 75.5,58.5 c 6.043169,1.18408 10.705093,6.052712 11.5,12.15625 -0.569435,-0.155806 -1.200273,-0.302525 -1.875,-0.4375 -2.262525,-0.452605 -5.108535,-0.783809 -8.28125,-0.9375 -0.130662,-3.556701 -0.470237,-6.762405 -0.96875,-9.25 C 75.761959,59.467174 75.626981,58.990925 75.5,58.5 z m -2.84375,12.09375 c 0.959338,0 1.895843,0.03282 2.8125,0.0625 C 75.48165,71.267751 75.5,71.871028 75.5,72.5 c 0,1.228616 -0.01449,2.438313 -0.0625,3.59375 -0.897358,0.0284 -1.811972,0.0625 -2.75,0.0625 -0.927373,0 -1.831062,-0.03473 -2.71875,-0.0625 -0.05109,-1.155437 -0.0625,-2.365134 -0.0625,-3.59375 0,-0.628972 0.01741,-1.232249 0.03125,-1.84375 0.895269,-0.02827 1.783025,-0.0625 2.71875,-0.0625 z M 68.5625,70.6875 c -0.01243,0.60601 -0.03125,1.189946 -0.03125,1.8125 0,1.22431 0.01541,2.407837 0.0625,3.5625 -3.125243,-0.150329 -5.92077,-0.471558 -8.09375,-0.90625 -0.784983,-0.157031 -1.511491,-0.316471 -2.125,-0.5 -0.107878,-0.704096 -0.1875,-1.422089 -0.1875,-2.15625 0,-0.115714 0.02849,-0.228688 0.03125,-0.34375 0.643106,-0.20284 1.389577,-0.390377 2.25,-0.5625 2.166953,-0.433487 4.97905,-0.75541 8.09375,-0.90625 z m 8.3125,0.03125 c 3.075121,0.15271 5.824455,0.446046 7.96875,0.875 0.857478,0.171534 1.630962,0.360416 2.28125,0.5625 0.0027,0.114659 0,0.228443 0,0.34375 0,0.735827 -0.07914,1.450633 -0.1875,2.15625 -0.598568,0.180148 -1.29077,0.34562 -2.0625,0.5 -2.158064,0.431708 -4.932088,0.754666 -8.03125,0.90625 0.04709,-1.154663 0.0625,-2.33819 0.0625,-3.5625 0,-0.611824 -0.01924,-1.185379 -0.03125,-1.78125 z M 57.15625,72.5625 c 0.0023,0.572772 0.06082,1.131112 0.125,1.6875 -0.125327,-0.05123 -0.266577,-0.10497 -0.375,-0.15625 -0.396499,-0.187528 -0.665288,-0.387337 -0.8125,-0.53125 -0.147212,-0.143913 -0.15625,-0.182756 -0.15625,-0.1875 0,-0.0047 -0.02221,-0.07484 0.125,-0.21875 0.147212,-0.143913 0.447251,-0.312472 0.84375,-0.5 0.07123,-0.03369 0.171867,-0.06006 0.25,-0.09375 z m 31.03125,0 c 0.08201,0.03503 0.175941,0.05872 0.25,0.09375 0.396499,0.187528 0.665288,0.356087 0.8125,0.5 0.14725,0.14391 0.15625,0.21405 0.15625,0.21875 0,0.0047 -0.009,0.04359 -0.15625,0.1875 -0.147212,0.143913 -0.416001,0.343722 -0.8125,0.53125 -0.09755,0.04613 -0.233314,0.07889 -0.34375,0.125 0.06214,-0.546289 0.09144,-1.094215 0.09375,-1.65625 z m -29.5,3.625 c 0.479308,0.123125 0.983064,0.234089 1.53125,0.34375 2.301781,0.460458 5.229421,0.787224 8.46875,0.9375 0.167006,2.84339 0.46081,5.433176 0.875,7.5 0.115218,0.574942 0.245268,1.063612 0.375,1.5625 -5.463677,-1.028179 -9.833074,-5.091831 -11.25,-10.34375 z m 27.96875,0 C 85.247546,81.408945 80.919274,85.442932 75.5,86.5 c 0.126981,-0.490925 0.261959,-0.967174 0.375,-1.53125 0.41419,-2.066824 0.707994,-4.65661 0.875,-7.5 3.204493,-0.15162 6.088346,-0.480068 8.375,-0.9375 0.548186,-0.109661 1.051942,-0.220625 1.53125,-0.34375 z M 70.0625,77.53125 c 0.865391,0.02589 1.723666,0.03125 2.625,0.03125 0.912062,0 1.782843,-0.0048 2.65625,-0.03125 -0.165173,2.736408 -0.453252,5.207651 -0.84375,7.15625 -0.152345,0.760206 -0.322989,1.438994 -0.5,2.03125 -0.437447,0.03919 -0.895856,0.0625 -1.34375,0.0625 -0.414943,0 -0.812719,-0.02881 -1.21875,-0.0625 -0.177011,-0.592256 -0.347655,-1.271044 -0.5,-2.03125 -0.390498,-1.948599 -0.700644,-4.419842 -0.875,-7.15625 z m 1.75,10.28125 c 0.284911,0.01545 0.554954,0.03125 0.84375,0.03125 0.325029,0 0.648588,-0.01171 0.96875,-0.03125 -0.05999,0.148763 -0.127309,0.31046 -0.1875,0.4375 -0.18789,0.396562 -0.386962,0.696416 -0.53125,0.84375 -0.144288,0.147334 -0.181857,0.125 -0.1875,0.125 -0.0056,0 -0.07446,0.02233 -0.21875,-0.125 C 72.355712,88.946416 72.18789,88.646562 72,88.25 71.939809,88.12296 71.872486,87.961263 71.8125,87.8125 z",
                this.svgCompassRotationMarker = "M 72.46875,22.03125 C 59.505873,22.050338 46.521615,27.004287 36.6875,36.875 L 47.84375,47.96875 C 61.521556,34.240041 83.442603,34.227389 97.125,47.90625 l 11.125,-11.125 C 98.401629,26.935424 85.431627,22.012162 72.46875,22.03125 z",
                this.showCompass = (0,
                e.defined)(this.terria) && this.enableCompass,
                this.heading = this.showCompass ? this.terria.scene.camera.heading : 0,
                this.isOrbiting = !1,
                this.orbitCursorAngle = 0,
                this.orbitCursorOpacity = 0,
                this.orbitLastTimestamp = 0,
                this.orbitFrame = void 0,
                this.orbitIsLook = !1,
                this.orbitMouseMoveFunction = void 0,
                this.orbitMouseUpFunction = void 0,
                this.isRotating = !1,
                this.rotateInitialCursorAngle = void 0,
                this.rotateFrame = void 0,
                this.rotateIsLook = !1,
                this.rotateMouseMoveFunction = void 0,
                this.rotateMouseUpFunction = void 0,
                this._unsubcribeFromPostRender = void 0,
                L.track(this, ["controls", "showCompass", "heading", "isOrbiting", "orbitCursorAngle", "isRotating"]);
                var i = this;
                function n() {
                    (0,
                    e.defined)(i.terria) ? (i._unsubcribeFromPostRender && (i._unsubcribeFromPostRender(),
                    i._unsubcribeFromPostRender = void 0),
                    i.showCompass = i.enableCompass,
                    i._unsubcribeFromPostRender = i.terria.scene.postRender.addEventListener((function() {
                        i.heading = i.terria.scene.camera.heading
                    }
                    ))) : (i._unsubcribeFromPostRender && (i._unsubcribeFromPostRender(),
                    i._unsubcribeFromPostRender = void 0),
                    i.showCompass = !1)
                }
                S.prototype.setNavigationLocked = function(e) {
                    this.navigationLocked = e,
                    this.controls && this.controls.length > 1 && this.controls[1].setNavigationLocked(this.navigationLocked)
                }
                ,
                this.eventHelper.add(this.terria.afterWidgetChanged, n, this),
                n()
            };
            S.prototype.destroy = function() {
                this.eventHelper.removeAll()
            }
            ,
            S.prototype.show = function(e) {
                const t = "</div>"
                  , i = ">"
                  , n = ' style="display: none;"'
                  , o = '<div class="compass"'
                  , a = void 0 === this.terria.options.enableCompassOuterRing || this.terria.options.enableCompassOuterRing ? 'title="" data-bind="visible: showCompass, event: { mousedown: handleMouseDown,touchstart:handleMouseDown, dblclick: handleDoubleClick }">' : 'title="" data-bind="visible: showCompass">'
                  , s = " <div class=\"compass-rotation-marker\" data-bind=\"visible: isOrbiting, style: { transform: 'rotate(-' + orbitCursorAngle + 'rad)', '-webkit-transform': 'rotate(-' + orbitCursorAngle + 'rad)', opacity: orbitCursorOpacity }"
                  , d = s + '"' + i + this.terria.options.compassRotationMarkerSvg
                  , c = (this.terria.options.compassRotationMarkerSvg ? d : " <div class=\"compass-rotation-marker\" data-bind=\"visible: isOrbiting, style: { transform: 'rotate(-' + orbitCursorAngle + 'rad)', '-webkit-transform': 'rotate(-' + orbitCursorAngle + 'rad)', opacity: orbitCursorOpacity }, cesiumSvgPath: { path: svgCompassRotationMarker, width: 145, height: 145 }\">") + t
                  , l = " <div class=\"compass-outer-ring\" title=\"\" data-bind=\"style: { transform: 'rotate(-' + heading + 'rad)', '-webkit-transform': 'rotate(-' + heading + 'rad)' }"
                  , p = l + '"' + i + this.terria.options.compassOuterRingSvg
                  , u = (this.terria.options.compassOuterRingSvg ? p : " <div class=\"compass-outer-ring\" title=\"\" data-bind=\"style: { transform: 'rotate(-' + heading + 'rad)', '-webkit-transform': 'rotate(-' + heading + 'rad)' }, cesiumSvgPath: { path: svgCompassOuterRing, width: 145, height: 145 }\">") + t
                  , h = ' <div class="compass-gyro" data-bind="css: { \'compass-gyro-active\': isOrbiting }'
                  , v = h + '"' + i + this.terria.options.compassGyroSvg
                  , m = a + '<div class="compass-outer-ring-background"></div>' + c + u + ' <div class="compass-gyro-background"></div>' + ((this.terria.options.compassGyroSvg ? v : ' <div class="compass-gyro" data-bind="css: { \'compass-gyro-active\': isOrbiting },cesiumSvgPath: { path: svgCompassGyro, width: 145, height: 145 } ">') + t) + t
                  , g = o + m
                  , f = o + n + m
                  , b = '<div class="navigation-controls"'
                  , C = ">\x3c!-- ko foreach: controls --\x3e<div data-bind=\"click: activate, attr: { title: $data.name }, css: $root.isLastControl($data) ? 'navigation-control-last' : 'navigation-control' \">   \x3c!-- ko if: $data.hasText --\x3e   <div data-bind=\"text: $data.text, css: $data.isActive ?  'navigation-control-icon-active ' + $data.cssClass : $data.cssClass\"></div>   \x3c!-- /ko --\x3e  \x3c!-- ko ifnot: $data.hasText --\x3e  \x3c!-- ko if: $data.svgIcon --\x3e  <div data-bind=\"cesiumSvgPath: { path: $data.svgIcon, width: $data.svgWidth, height: $data.svgHeight }, css: $data.isActive ?  'navigation-control-icon-active ' + $data.cssClass : $data.cssClass\"></div>  \x3c!-- /ko --\x3e  \x3c!-- ko ifnot: $data.svgIcon --\x3e  \x3c!-- ko if: $data.resetSvg --\x3e  <div class=\"navigation-control-icon-svg\">" + this.terria.options.resetSvg + '</div>  \x3c!-- /ko --\x3e  \x3c!-- ko ifnot: $data.resetSvg --\x3e  \x3c!-- ko if: $data.zoomInSvg --\x3e  <div class="navigation-control-icon-svg">' + this.terria.options.zoomInSvg + '</div>  \x3c!-- /ko --\x3e  \x3c!-- ko ifnot: $data.zoomInSvg --\x3e  \x3c!-- ko if: $data.zoomOutSvg --\x3e  <div class="navigation-control-icon-svg">' + this.terria.options.zoomOutSvg + "</div>  \x3c!-- /ko --\x3e  \x3c!-- /ko --\x3e  \x3c!-- /ko --\x3e  \x3c!-- /ko --\x3e  \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e" + t
                  , x = b + C
                  , y = b + n + i + C
                  , M = (this.enableCompass ? g : f) + (this.enableZoomControls ? x : y);
                r(M, e, this)
            }
            ,
            S.prototype.add = function(e) {
                this.controls.push(e)
            }
            ,
            S.prototype.remove = function(e) {
                this.controls.remove(e)
            }
            ,
            S.prototype.isLastControl = function(e) {
                return e === this.controls[this.controls.length - 1]
            }
            ;
            var E = new e.Cartesian2;
            const T = e => {
                let t;
                t = e.touches ? e.touches[0] : e;
                const {clientX: i, clientY: n} = t;
                return {
                    clientX: i,
                    clientY: n
                }
            }
            ;
            S.prototype.handleMouseDown = function(t, i) {
                const {clientX: n, clientY: o} = T(i);
                if (this.terria.scene.mode === e.SceneMode.MORPHING)
                    return !0;
                if (t.navigationLocked)
                    return !0;
                var r = i.currentTarget
                  , a = i.currentTarget.getBoundingClientRect()
                  , s = a.width / 2
                  , d = new e.Cartesian2((a.right - a.left) / 2,(a.bottom - a.top) / 2)
                  , c = new e.Cartesian2(n - a.left,o - a.top)
                  , l = e.Cartesian2.subtract(c, d, E)
                  , p = e.Cartesian2.magnitude(l) / s;
                if (p < 50 / 145)
                    !function(t, i, n) {
                        var o = t.terria.scene
                          , r = o.screenSpaceCameraController;
                        if (o.mode === e.SceneMode.MORPHING || !r.enableInputs)
                            return;
                        if (t.navigationLocked)
                            return !0;
                        switch (o.mode) {
                        case e.SceneMode.COLUMBUS_VIEW:
                            if (r.enableLook)
                                break;
                            if (!r.enableTranslate || !r.enableTilt)
                                return;
                            break;
                        case e.SceneMode.SCENE3D:
                            if (r.enableLook)
                                break;
                            if (!r.enableTilt || !r.enableRotate)
                                return;
                            break;
                        case e.SceneMode.SCENE2D:
                            if (!r.enableTranslate)
                                return
                        }
                        O(t),
                        (0,
                        e.defined)(t.orbitTickFunction) && t.terria.clock.onTick.removeEventListener(t.orbitTickFunction);
                        t.orbitMouseMoveFunction = void 0,
                        t.orbitMouseUpFunction = void 0,
                        t.orbitTickFunction = void 0,
                        t.isOrbiting = !0,
                        t.orbitLastTimestamp = (0,
                        e.getTimestamp)();
                        var a = o.camera;
                        if ((0,
                        e.defined)(t.terria.trackedEntity))
                            t.orbitFrame = void 0,
                            t.orbitIsLook = !1;
                        else {
                            var s = M.getCameraFocus(t.terria, !0, z);
                            (0,
                            e.defined)(s) ? (t.orbitFrame = e.Transforms.eastNorthUpToFixedFrame(s, o.globe.ellipsoid, R),
                            t.orbitIsLook = !1) : (t.orbitFrame = e.Transforms.eastNorthUpToFixedFrame(a.positionWC, o.globe.ellipsoid, R),
                            t.orbitIsLook = !0)
                        }
                        function d(i, n) {
                            var o = Math.atan2(-i.y, i.x);
                            t.orbitCursorAngle = e.Math.zeroToTwoPi(o - e.Math.PI_OVER_TWO);
                            var r = e.Cartesian2.magnitude(i)
                              , a = n / 2
                              , s = Math.min(r / a, 1)
                              , d = .5 * s * s + .5;
                            t.orbitCursorOpacity = d
                        }
                        t.orbitTickFunction = function(i) {
                            var n, r = (0,
                            e.getTimestamp)(), s = (r - t.orbitLastTimestamp) * (2.5 * (t.orbitCursorOpacity - .5) / 1e3), d = t.orbitCursorAngle + e.Math.PI_OVER_TWO, c = Math.cos(d) * s, l = Math.sin(d) * s;
                            if (t.navigationLocked)
                                return !0;
                            (0,
                            e.defined)(t.orbitFrame) && (n = e.Matrix4.clone(a.transform, D),
                            a.lookAtTransform(t.orbitFrame)),
                            o.mode === e.SceneMode.SCENE2D ? a.move(new e.Cartesian3(c,l,0), Math.max(o.canvas.clientWidth, o.canvas.clientHeight) / 100 * a.positionCartographic.height * s) : t.orbitIsLook ? (a.look(e.Cartesian3.UNIT_Z, -c),
                            a.look(a.right, -l)) : (a.rotateLeft(c),
                            a.rotateUp(l)),
                            (0,
                            e.defined)(t.orbitFrame) && a.lookAtTransform(n),
                            t.orbitLastTimestamp = r
                        }
                        ,
                        t.orbitMouseMoveFunction = function(t) {
                            const {clientX: n, clientY: o} = T(t);
                            var r = i.getBoundingClientRect()
                              , a = new e.Cartesian2((r.right - r.left) / 2,(r.bottom - r.top) / 2)
                              , s = new e.Cartesian2(n - r.left,o - r.top);
                            d(e.Cartesian2.subtract(s, a, E), r.width)
                        }
                        ,
                        t.orbitMouseUpFunction = function(i) {
                            t.isOrbiting = !1,
                            O(t),
                            (0,
                            e.defined)(t.orbitTickFunction) && t.terria.clock.onTick.removeEventListener(t.orbitTickFunction),
                            t.orbitMouseMoveFunction = void 0,
                            t.orbitMouseUpFunction = void 0,
                            t.orbitTickFunction = void 0
                        }
                        ,
                        (e => {
                            document.addEventListener("mousemove", e.orbitMouseMoveFunction, !1),
                            document.addEventListener("touchmove", e.orbitMouseMoveFunction, !1),
                            document.addEventListener("mouseup", e.orbitMouseUpFunction, !1),
                            document.addEventListener("touchend", e.orbitMouseUpFunction, !1)
                        }
                        )(t),
                        t.terria.clock.onTick.addEventListener(t.orbitTickFunction),
                        d(n, i.getBoundingClientRect().width)
                    }(this, r, l);
                else {
                    if (!(p < 1))
                        return !0;
                    !function(t, i, n) {
                        var o, r = t.terria.scene, a = r.camera, s = r.screenSpaceCameraController;
                        if (r.mode === e.SceneMode.MORPHING || r.mode === e.SceneMode.SCENE2D || !s.enableInputs)
                            return;
                        if (t.navigationLocked)
                            return !0;
                        if (!s.enableLook && (r.mode === e.SceneMode.COLUMBUS_VIEW || r.mode === e.SceneMode.SCENE3D && !s.enableRotate))
                            return;
                        if (I(t),
                        t.rotateMouseMoveFunction = void 0,
                        t.rotateMouseUpFunction = void 0,
                        t.isRotating = !0,
                        t.rotateInitialCursorAngle = Math.atan2(-n.y, n.x),
                        (0,
                        e.defined)(t.terria.trackedEntity))
                            t.rotateFrame = void 0,
                            t.rotateIsLook = !1;
                        else {
                            var d = M.getCameraFocus(t.terria, !0, z);
                            (0,
                            e.defined)(d) && (r.mode !== e.SceneMode.COLUMBUS_VIEW || s.enableLook || s.enableTranslate) ? (t.rotateFrame = e.Transforms.eastNorthUpToFixedFrame(d, r.globe.ellipsoid, R),
                            t.rotateIsLook = !1) : (t.rotateFrame = e.Transforms.eastNorthUpToFixedFrame(a.positionWC, r.globe.ellipsoid, R),
                            t.rotateIsLook = !0)
                        }
                        (0,
                        e.defined)(t.rotateFrame) && (o = e.Matrix4.clone(a.transform, D),
                        a.lookAtTransform(t.rotateFrame));
                        t.rotateInitialCameraAngle = -a.heading,
                        (0,
                        e.defined)(t.rotateFrame) && a.lookAtTransform(o);
                        t.rotateMouseMoveFunction = function(n) {
                            const {clientX: o, clientY: r} = T(n);
                            var a, s = i.getBoundingClientRect(), d = new e.Cartesian2((s.right - s.left) / 2,(s.bottom - s.top) / 2), c = new e.Cartesian2(o - s.left,r - s.top), l = e.Cartesian2.subtract(c, d, E), p = Math.atan2(-l.y, l.x) - t.rotateInitialCursorAngle, u = e.Math.zeroToTwoPi(t.rotateInitialCameraAngle - p), h = t.terria.scene.camera;
                            (0,
                            e.defined)(t.rotateFrame) && (a = e.Matrix4.clone(h.transform, D),
                            h.lookAtTransform(t.rotateFrame));
                            var v = -h.heading;
                            h.rotateRight(u - v),
                            (0,
                            e.defined)(t.rotateFrame) && h.lookAtTransform(a)
                        }
                        ,
                        t.rotateMouseUpFunction = function(e) {
                            t.isRotating = !1,
                            I(t),
                            t.rotateMouseMoveFunction = void 0,
                            t.rotateMouseUpFunction = void 0
                        }
                        ,
                        (e => {
                            document.addEventListener("mousemove", e.rotateMouseMoveFunction, !1),
                            document.addEventListener("touchmove", e.rotateMouseMoveFunction, !1),
                            document.addEventListener("mouseup", e.rotateMouseUpFunction, !1),
                            document.addEventListener("touchend", e.rotateMouseUpFunction, !1)
                        }
                        )(t)
                    }(this, r, l)
                }
            }
            ;
            var D = new e.Matrix4
              , R = new e.Matrix4
              , z = new e.Cartesian3;
            S.prototype.handleDoubleClick = function(t, i) {
                var n = t.terria.scene
                  , o = n.camera
                  , r = n.screenSpaceCameraController;
                if (n.mode === e.SceneMode.MORPHING || !r.enableInputs)
                    return !0;
                if (t.navigationLocked)
                    return !0;
                if (n.mode !== e.SceneMode.COLUMBUS_VIEW || r.enableTranslate) {
                    if (n.mode === e.SceneMode.SCENE3D || n.mode === e.SceneMode.COLUMBUS_VIEW) {
                        if (!r.enableLook)
                            return;
                        if (n.mode === e.SceneMode.SCENE3D && !r.enableRotate)
                            return
                    }
                    var a = M.getCameraFocus(t.terria, !0, z);
                    if ((0,
                    e.defined)(a)) {
                        var s = n.globe.ellipsoid.cartographicToCartesian(o.positionCartographic, new e.Cartesian3)
                          , d = n.globe.ellipsoid.geodeticSurfaceNormal(a)
                          , c = new e.BoundingSphere(a,0);
                        o.flyToBoundingSphere(c, {
                            offset: new e.HeadingPitchRange(0,e.Math.PI_OVER_TWO - e.Cartesian3.angleBetween(d, o.directionWC),e.Cartesian3.distance(s, a)),
                            duration: 1.5
                        })
                    } else
                        this.controls[1].resetView()
                }
            }
            ,
            S.create = function(e) {
                var t = new S(e);
                return t.show(e.container),
                t
            }
            ;
            const O = e => {
                document.removeEventListener("mousemove", e.orbitMouseMoveFunction, !1),
                document.removeEventListener("touchmove", e.orbitMouseMoveFunction, !1),
                document.removeEventListener("mouseup", e.orbitMouseUpFunction, !1),
                document.removeEventListener("touchend", e.orbitMouseUpFunction, !1)
            }
              , I = e => {
                document.removeEventListener("mousemove", e.rotateMouseMoveFunction, !1),
                document.removeEventListener("touchmove", e.rotateMouseMoveFunction, !1),
                document.removeEventListener("mouseup", e.rotateMouseUpFunction, !1),
                document.removeEventListener("touchend", e.rotateMouseUpFunction, !1)
            }
            ;
            const A = S;
            var _ = e.Event
              , N = function(e) {
                U.apply(this, arguments),
                this._onDestroyListeners = []
            };
            function U(t, i) {
                if (!(0,
                e.defined)(t))
                    throw new e.DeveloperError("CesiumWidget or Viewer is required.");
                var n = (0,
                e.defined)(t.cesiumWidget) ? t.cesiumWidget : t
                  , o = document.createElement("div");
                o.className = "cesium-widget-cesiumNavigationContainer",
                n.container.appendChild(o),
                this.terria = t,
                this.terria.options = (0,
                e.defined)(i) ? i : {},
                this.terria.afterWidgetChanged = new _,
                this.terria.beforeWidgetChanged = new _,
                this.container = o,
                (0,
                e.defined)(this.terria.options.enableDistanceLegend) && !this.terria.options.enableDistanceLegend || (this.distanceLegendDiv = document.createElement("div"),
                o.appendChild(this.distanceLegendDiv),
                this.distanceLegendDiv.setAttribute("id", "distanceLegendDiv"),
                this.distanceLegendViewModel = l.create({
                    container: this.distanceLegendDiv,
                    terria: this.terria,
                    mapElement: o,
                    enableDistanceLegend: !0
                })),
                (0,
                e.defined)(this.terria.options.enableZoomControls) && !this.terria.options.enableZoomControls || (0,
                e.defined)(this.terria.options.enableCompass) && !this.terria.options.enableCompass ? !(0,
                e.defined)(this.terria.options.enableZoomControls) || this.terria.options.enableZoomControls || (0,
                e.defined)(this.terria.options.enableCompass) && !this.terria.options.enableCompass ? (0,
                e.defined)(this.terria.options.enableZoomControls) && !this.terria.options.enableZoomControls || !(0,
                e.defined)(this.terria.options.enableCompass) || this.terria.options.enableCompass ? (0,
                e.defined)(this.terria.options.enableZoomControls) && !this.terria.options.enableZoomControls && (0,
                e.defined)(this.terria.options.enableCompass) && this.terria.options.enableCompass : (this.navigationDiv = document.createElement("div"),
                this.navigationDiv.setAttribute("id", "navigationDiv"),
                o.appendChild(this.navigationDiv),
                this.navigationViewModel = A.create({
                    container: this.navigationDiv,
                    terria: this.terria,
                    enableZoomControls: !0,
                    enableCompass: !1
                })) : (this.navigationDiv = document.createElement("div"),
                this.navigationDiv.setAttribute("id", "navigationDiv"),
                o.appendChild(this.navigationDiv),
                this.navigationViewModel = A.create({
                    container: this.navigationDiv,
                    terria: this.terria,
                    enableZoomControls: !1,
                    enableCompass: !0
                })) : (this.navigationDiv = document.createElement("div"),
                this.navigationDiv.setAttribute("id", "navigationDiv"),
                o.appendChild(this.navigationDiv),
                this.navigationViewModel = A.create({
                    container: this.navigationDiv,
                    terria: this.terria,
                    enableZoomControls: !0,
                    enableCompass: !0
                }))
            }
            N.prototype.distanceLegendViewModel = void 0,
            N.prototype.navigationViewModel = void 0,
            N.prototype.navigationDiv = void 0,
            N.prototype.distanceLegendDiv = void 0,
            N.prototype.terria = void 0,
            N.prototype.container = void 0,
            N.prototype._onDestroyListeners = void 0,
            N.prototype._navigationLocked = !1,
            N.prototype.setNavigationLocked = function(e) {
                this._navigationLocked = e,
                this.navigationViewModel.setNavigationLocked(this._navigationLocked)
            }
            ,
            N.prototype.getNavigationLocked = function() {
                return this._navigationLocked
            }
            ,
            N.prototype.destroy = function() {
                (0,
                e.defined)(this.navigationViewModel) && this.navigationViewModel.destroy(),
                (0,
                e.defined)(this.distanceLegendViewModel) && this.distanceLegendViewModel.destroy(),
                (0,
                e.defined)(this.navigationDiv) && this.navigationDiv.parentNode.removeChild(this.navigationDiv),
                delete this.navigationDiv,
                (0,
                e.defined)(this.distanceLegendDiv) && this.distanceLegendDiv.parentNode.removeChild(this.distanceLegendDiv),
                delete this.distanceLegendDiv,
                (0,
                e.defined)(this.container) && this.container.parentNode.removeChild(this.container),
                delete this.container;
                for (var t = 0; t < this._onDestroyListeners.length; t++)
                    this._onDestroyListeners[t]()
            }
            ,
            N.prototype.addOnDestroyListener = function(e) {
                "function" == typeof e && this._onDestroyListeners.push(e)
            }
            ;
            const W = N
        }
        )(),
        o = o.default
    }
    )()
}
));
