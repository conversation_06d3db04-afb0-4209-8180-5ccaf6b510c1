// 基于经纬度绘制剖面线

class ProfileAnalysis {
    constructor(viewer) {
        this.viewer = viewer;
        this.isActive = false;
        this.drawingMode = false;
        this.profilePoints = [];
        this.profileEntity = null;
        this.profileChart = null;
        this.pointEntities = []; // 存储标注点实体

        // 初始化面板
        this.panel = document.getElementById('profileAnalysisPanel');
        this.startCoord = document.getElementById('startCoord');
        this.endCoord = document.getElementById('endCoord');
        this.profileLength = document.getElementById('profileLength');
        
        // 初始化事件监听
        this.initEventListeners();
    }

    initEventListeners() {
        // 按钮点击事件
        const drawBtn = document.getElementById('drawProfile');
        const clearBtn = document.getElementById('clearProfile');
        const exitBtn = document.getElementById('exitProfile');

        if (!drawBtn || !clearBtn || !exitBtn) {
            console.error('Some buttons not found');
            return;
        }

        drawBtn.addEventListener('click', () => this.startDrawing());
        clearBtn.addEventListener('click', () => this.clearAnalysis());
        exitBtn.addEventListener('click', () => this.deactivate());

        // 鼠标事件监听
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    }

    togglePanel(button) {
        if (this.panel.style.display === 'none' || !this.panel.style.display) {
            this.showPanel(button);
        } else {
            this.deactivate();
        }
    }

    activate() {
        this.isActive = true;
        this.panel.style.display = 'block';
        
        // 初始化ECharts
        setTimeout(() => {
            const chartDom = document.getElementById('profileChart');
            if (!chartDom) return;

            if (this.profileChart) {
                this.profileChart.dispose();
            }

            this.profileChart = echarts.init(chartDom);
            
            // 设置初始空白图表
            const initOption = {
                grid: {
                    left: '12%',
                    right: '5%',
                    bottom: '15%',
                    top: '12%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    name: '距离(km)',
                    nameTextStyle: { color: '#333' },
                    axisLine: { lineStyle: { color: '#333' } },
                    splitLine: {
                        show: true,
                        lineStyle: { color: '#eee' }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '高度(m)',
                    nameTextStyle: { color: '#333' },
                    axisLine: { lineStyle: { color: '#333' } },
                    splitLine: {
                        show: true,
                        lineStyle: { color: '#eee' }
                    }
                }
            };

            this.profileChart.setOption(initOption);
            
            // 强制重绘
            window.addEventListener('resize', () => {
                if (this.profileChart) {
                    this.profileChart.resize();
                }
            });
            this.profileChart.resize();
        }, 200);
    }

    deactivate() {
        this.isActive = false;
        this.drawingMode = false;
        this.panel.style.display = 'none';
        this.clearAnalysis();
        if (this.handler) {
            this.handler.destroy();
            this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        }
        if (this.profileChart) {
            this.profileChart.dispose();
            this.profileChart = null;
        }
    }

    startDrawing() {
        this.drawingMode = true;
        this.clearAnalysis();
        
        // 设置鼠标状态
        this.viewer.container.style.cursor = 'crosshair';
        
        // 左键点击事件
        this.handler.setInputAction((click) => {
            const position = this.getCurrentMousePosition(this.viewer.scene, click.position);
            if (Cesium.defined(position)) {
                const cartographic = Cesium.Cartographic.fromCartesian(position);
                const longitude = Cesium.Math.toDegrees(cartographic.longitude);
                const latitude = Cesium.Math.toDegrees(cartographic.latitude);
                const height = this.viewer.scene.globe.getHeight(cartographic) || 0;

                this.profilePoints.push([longitude, latitude, height]);
                
                // 添加标注点
                const pointType = this.profilePoints.length === 1 ? '起点' : '终点';
                const pointColor = this.profilePoints.length === 1 ? Cesium.Color.fromCssColorString('#1890ff') : Cesium.Color.fromCssColorString('#ff4d4f');
                const pointEntity = this.viewer.entities.add({
                    position: position,
                    point: {
                        pixelSize: 12,
                        color: pointColor,
                        outlineColor: Cesium.Color.WHITE,
                        outlineWidth: 2,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                    label: {
                        text: pointType,
                        font: '12px sans-serif',
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                        outlineWidth: 2,
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                        pixelOffset: new Cesium.Cartesian2(0, -10),
                        fillColor: Cesium.Color.WHITE,
                        outlineColor: pointColor,
                        showBackground: true,
                        backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
                        backgroundPadding: new Cesium.Cartesian2(4, 4),
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                });
                
                this.pointEntities.push(pointEntity);
                
                if (this.profilePoints.length === 2) {
                    this.drawingMode = false;
                    this.viewer.container.style.cursor = 'default';
                    this.drawProfileLine();
                    this.calculateProfile();
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    getCurrentMousePosition(scene, position) {
        let cartesian;
        const pickedObject = scene.pick(position);
        
        if (scene.pickPositionSupported && Cesium.defined(pickedObject)) {
            cartesian = scene.pickPosition(position);
            if (Cesium.defined(cartesian)) {
                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                if (cartographic.height >= -500) {
                    return cartesian;
                }
            }
        }

        if (scene.mode === Cesium.SceneMode.SCENE3D) {
            const pickRay = scene.camera.getPickRay(position);
            cartesian = scene.globe.pick(pickRay, scene);
        } else {
            cartesian = scene.camera.pickEllipsoid(position, scene.globe.ellipsoid);
        }
        return cartesian;
    }

    drawProfileLine() {
        if (this.profilePoints.length !== 2) return;

        // 绘制剖面线
        this.profileEntity = this.viewer.entities.add({
            polyline: {
                positions: Cesium.Cartesian3.fromDegreesArrayHeights([
                    this.profilePoints[0][0], this.profilePoints[0][1], this.profilePoints[0][2],
                    this.profilePoints[1][0], this.profilePoints[1][1], this.profilePoints[1][2]
                ]),
                width: 3,
                material: new Cesium.PolylineDashMaterialProperty({
                    color: Cesium.Color.YELLOW,
                    dashLength: 16.0
                }),
                clampToGround: true
            }
        });

        // 更新面板信息
        this.updatePanelInfo();
    }

    updatePanelInfo() {
        const start = this.profilePoints[0];
        const end = this.profilePoints[1];
        
        this.startCoord.textContent = `${start[0].toFixed(6)}°, ${start[1].toFixed(6)}°`;
        this.endCoord.textContent = `${end[0].toFixed(6)}°, ${end[1].toFixed(6)}°`;
        
        // 计算距离
        const line = turf.lineString([[start[0], start[1]], [end[0], end[1]]]);
        const length = turf.length(line, {units: 'kilometers'});
        this.profileLength.textContent = `${length.toFixed(2)} 公里`;
    }

    calculateProfile() {
        const start = this.profilePoints[0];
        const end = this.profilePoints[1];
        const samples = [];
        const step = 100; // 采样点数量
        
        // 使用 Cesium.Math.lerp 进行插值采样
        for (let i = 0; i < step; i++) {
            const offset = i / (step - 1);
            
            // 经度插值
            const lon = Cesium.Math.lerp(start[0], end[0], offset);
            // 纬度插值
            const lat = Cesium.Math.lerp(start[1], end[1], offset);
            
            // 获取当前点的高程
            const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
            const height = this.viewer.scene.globe.getHeight(cartographic) || 0;
            
            // 计算当前点到起点的距离
            const currentLine = turf.lineString([[start[0], start[1]], [lon, lat]]);
            const distance = turf.length(currentLine, {units: 'kilometers'});
            
            samples.push([distance, height]);
        }

        this.updateChart(samples);
    }

    updateChart(samples) {
        if (!this.profileChart) return;

        const heights = samples.map(item => item[1]);
        const minHeight = Math.min(...heights);
        const maxHeight = Math.max(...heights);
        const heightBuffer = (maxHeight - minHeight) * 0.1;

        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    return `距起点：${params[0].value[0].toFixed(2)}km<br/>高度：${params[0].value[1].toFixed(0)}m`;
                }
            },
            grid: {
                left: '15%',      // 增加左边距
                right: '10%',     // 增加右边距
                bottom: '15%',    // 增加底部边距
                top: '15%',       // 增加顶部边距
                containLabel: false  // 改为false以确保标题在grid之外
            },
            xAxis: {
                type: 'value',
                name: '距离(km)',
                nameLocation: 'middle',  // 改用middle确保更好的居中效果
                nameGap: 30,            // 增加间距
                nameTextStyle: {
                    color: '#333',
                    fontSize: 12,
                    fontWeight: 'bold',  // 加粗使其更清晰
                    padding: [15, 0, 0, 0]  // 增加顶部内边距
                },
                axisLine: {
                    lineStyle: { color: '#333' }
                },
                splitLine: {
                    show: true,
                    lineStyle: { color: '#eee' }
                },
                axisLabel: {
                    fontSize: 11,
                    margin: 12,          // 增加标签间距
                    color: '#666'
                }
            },
            yAxis: {
                type: 'value',
                name: '高度(m)',
                nameLocation: 'middle',  // 改用middle确保更好的居中效果
                nameGap: 45,            // 增加间距
                nameTextStyle: {
                    color: '#333',
                    fontSize: 12,
                    fontWeight: 'bold',  // 加粗使其更清晰
                    padding: [0, 0, 15, 0]  // 增加底部内边距
                },
                min: Math.floor(minHeight - heightBuffer),
                max: Math.ceil(maxHeight + heightBuffer),
                axisLine: {
                    lineStyle: { color: '#333' }
                },
                splitLine: {
                    show: true,
                    lineStyle: { color: '#eee' }
                },
                axisLabel: {
                    fontSize: 11,
                    margin: 12,          // 增加标签间距
                    color: '#666'
                }
            },
            series: [{
                type: 'line',
                data: samples,
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 2,
                    color: '#1890ff'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: 'rgba(24,144,255,0.3)'
                    }, {
                        offset: 1,
                        color: 'rgba(24,144,255,0.1)'
                    }])
                }
            }]
        };

        this.profileChart.setOption(option, true);
        this.profileChart.resize();
    }

    clearAnalysis() {
        if (this.profileEntity) {
            this.viewer.entities.remove(this.profileEntity);
            this.profileEntity = null;
        }
        
        // 清除标注点实体
        this.pointEntities.forEach(entity => {
            this.viewer.entities.remove(entity);
        });
        this.pointEntities = [];
        
        this.profilePoints = [];
        this.startCoord.textContent = '-';
        this.endCoord.textContent = '-';
        this.profileLength.textContent = '-';
        
        if (this.profileChart) {
            this.profileChart.setOption({
                series: []
            });
        }
    }

    // 添加显示面板方法
    showPanel(button) {
        console.log('ProfileAnalysis.showPanel 被调用');
        if (!this.panel) {
            console.error('剖面分析面板不存在');
            this.panel = document.getElementById('profileAnalysisPanel');
            if (!this.panel) {
                console.error('尝试重新获取剖面分析面板失败');
                return;
            }
            console.log('重新获取到剖面分析面板');
        }
        
        console.log('找到剖面分析面板');
        
        // 确保面板可见性设置正确
        this.panel.style.display = 'block';
        console.log('剖面分析面板显示属性已设置为block');
        
        // 检查PanelPositioner是否可用
        if (typeof PanelPositioner === 'undefined') {
            console.error('PanelPositioner未定义，无法定位面板');
            // 默认位置
            this.panel.style.position = 'fixed';
            this.panel.style.top = '100px';
            this.panel.style.left = '100px';
            console.log('已使用默认位置');
        } else {
            console.log('正在使用PanelPositioner设置面板位置');
            try {
                // 使用PanelPositioner设置面板位置
                PanelPositioner.setPosition(button, this.panel, {
                    preferredPosition: 'right',
                    gap: 10
                });
                console.log('面板位置设置完成');
            } catch (error) {
                console.error('设置面板位置时出错:', error);
                // 默认位置
                this.panel.style.position = 'fixed';
                this.panel.style.top = '100px';
                this.panel.style.left = '100px';
                console.log('出错后已使用默认位置');
            }
        }
        
        // 强制显示检查
        setTimeout(() => {
            if (this.panel.style.display !== 'block') {
                console.warn('延迟检查: 面板仍未显示，强制设置为显示');
                this.panel.style.display = 'block';
            }
            // 触发布局重新计算
            this.panel.offsetHeight;
        }, 100);
        
        // 激活功能
        this.activate();
        console.log('剖面分析功能已激活');
    }
}

// 全局剖面分析面板切换函数
window.toggleProfilePanel = function() {
    console.log('直接调用toggleProfilePanel函数');
    const panel = document.getElementById('profileAnalysisPanel');
    if (panel) {
        const currentDisplay = window.getComputedStyle(panel).display;
        console.log('剖面分析面板当前显示状态:', currentDisplay);
        
        if (currentDisplay === 'none') {
            console.log('直接显示剖面分析面板');
            panel.style.display = 'block';
            
            // 隐藏其他面板
            ['searchPanel', 'terrainDigPanel', 'measureToolContainer'].forEach(id => {
                const otherPanel = document.getElementById(id);
                if (otherPanel) otherPanel.style.display = 'none';
            });
            
            // 尝试设置位置
            const toggleBtn = document.getElementById('toggleProfileAnalysis');
            if (toggleBtn && window.PanelPositioner) {
                try {
                    window.PanelPositioner.setPosition(toggleBtn, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                } catch (e) {
                    console.error('面板定位出错:', e);
                }
            } else {
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
            }
            
            // 获取ProfileAnalysis实例并激活
            if (window.profileAnalysis) {
                window.profileAnalysis.activate();
            }
        } else {
            console.log('直接隐藏剖面分析面板');
            panel.style.display = 'none';
            
            // 获取ProfileAnalysis实例并停用
            if (window.profileAnalysis) {
                window.profileAnalysis.deactivate();
            }
        }
    } else {
        console.error('剖面分析面板元素不存在');
    }
};

// 添加静态初始化方法
ProfileAnalysis.init = function(viewer, toggleBtnId = 'toggleProfileAnalysis') {
    console.log('ProfileAnalysis.init 被调用，初始化剖面分析功能');
    const profileAnalysis = new ProfileAnalysis(viewer);
    
    // 获取切换按钮
    const toggleBtn = document.getElementById(toggleBtnId);
    console.log('剖面分析按钮元素:', toggleBtn ? '已找到' : '未找到');
    
    if (toggleBtn) {
        // 清除现有事件监听器，避免重复绑定
        const newToggleBtn = toggleBtn.cloneNode(true);
        if (toggleBtn.parentNode) {
            toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
            console.log('已清除并替换剖面分析按钮元素，避免事件重复绑定');
        }
        
        // 重新绑定事件监听器
        newToggleBtn.addEventListener('click', function(e) {
            console.log('剖面分析按钮被点击 (ProfileAnalysis.init)');
            const panel = document.getElementById('profileAnalysisPanel');
            console.log('剖面分析面板元素:', panel ? '已找到' : '未找到', '当前显示状态:', panel ? panel.style.display : 'N/A');
            
            if (panel) {
                if (panel.style.display === 'none' || !panel.style.display) {
                    // 隐藏其他面板
                    const otherPanels = ['searchPanel', 'terrainDigPanel', 'measureToolContainer'];
                    otherPanels.forEach(panelId => {
                        const otherPanel = document.getElementById(panelId);
                        if (otherPanel) {
                            otherPanel.style.display = 'none';
                            console.log(`已隐藏面板: ${panelId}`);
                        }
                    });
                    
                    // 显示剖面分析面板
                    console.log('正在显示剖面分析面板...');
                    profileAnalysis.showPanel(e.currentTarget);
                } else {
                    console.log('正在隐藏剖面分析面板...');
                    profileAnalysis.deactivate();
                }
            } else {
                console.error('未找到剖面分析面板元素!');
            }
        });
        
        console.log('剖面分析按钮事件绑定完成');
    } else {
        console.error('未找到剖面分析按钮元素，无法绑定事件!');
    }
    
    return profileAnalysis;
};
