/**
 * 3D建筑管理工具
 * 实现在地图上添加、编辑、删除3D建筑模型的功能
 */
class Building3DTool {
    /**
     * 构造函数
     * @param {Object} viewer - Cesium viewer实例
     * @param {Object} options - 配置选项
     */
    constructor(viewer, options = {}) {
        this.viewer = viewer;
        this.options = Object.assign({
            defaultHeight: 50,
            buildingStyle: {
                color: '#4A90E2',
                opacity: 0.8,
                outlineColor: '#2E5C8A'
            }
        }, options);
        
        this.isActive = false;
        this.buildings = [];
        this.selectedBuilding = null;
        this.drawingMode = false;
        this.currentBuildingType = { name: '住宅楼', height: 30, color: '#4A90E2' };
        
        this._initEventHandlers();
    }
    
    /**
     * 初始化事件处理器
     * @private
     */
    _initEventHandlers() {
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);
        
        this.handler.setInputAction((click) => {
            if (!this.isActive) return;
            
            if (this.drawingMode) {
                const cartesian = this._getCartesianFromClick(click.position);
                if (cartesian) {
                    this._addBuilding(cartesian);
                    this.drawingMode = false;
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }
    
    /**
     * 从点击位置获取笛卡尔坐标
     * @param {Object} clickPosition - 屏幕点击位置
     * @returns {Cesium.Cartesian3} 笛卡尔坐标
     * @private
     */
    _getCartesianFromClick(clickPosition) {
        let cartesian = this.viewer.scene.pickPosition(clickPosition);
        if (!cartesian) {
            const ray = this.viewer.camera.getPickRay(clickPosition);
            cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
        }
        return cartesian;
    }
    
    /**
     * 添加新建筑
     * @param {Cesium.Cartesian3} position - 建筑位置
     * @private
     */
    _addBuilding(position) {
        const id = new Date().getTime().toString();
        const buildingData = {
            id: id,
            name: this.currentBuildingType.name,
            height: this.currentBuildingType.height,
            color: this.currentBuildingType.color
        };
        
        const building = this.viewer.entities.add({
            id: id,
            position: position,
            box: {
                dimensions: new Cesium.Cartesian3(20.0, 20.0, buildingData.height),
                material: Cesium.Color.fromCssColorString(buildingData.color).withAlpha(0.8),
                outline: true,
                outlineColor: Cesium.Color.fromCssColorString('#2E5C8A'),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
            label: {
                text: buildingData.name,
                font: '16px 微软雅黑',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -buildingData.height/2 - 10)
            },
            properties: {
                isBuilding: true,
                buildingData: buildingData
            }
        });
        
        this.buildings.push(building);
        return building;
    }
    
    /**
     * 激活工具
     */
    activate() {
        this.isActive = true;
    }
    
    /**
     * 停用工具
     */
    deactivate() {
        this.isActive = false;
        this.drawingMode = false;
    }
    
    /**
     * 开始绘制建筑
     */
    startDraw() {
        this.drawingMode = true;
    }
    
    /**
     * 清除所有建筑
     */
    clearBuildings() {
        for (let building of this.buildings) {
            this.viewer.entities.remove(building);
        }
        this.buildings = [];
    }
}

// 导出类
window.Building3DTool = Building3DTool; 