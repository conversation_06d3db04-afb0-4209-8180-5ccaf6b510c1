/**
 * 书签管理工具重定向文件
 * 该文件用于兼容旧的引用路径，并确保BookmarkTool类正确加载
 */

// 确保先加载BookmarkHandler.js中的BookmarkTool类
if (typeof BookmarkTool === 'undefined') {
    // 动态加载BookmarkHandler.js
    const script = document.createElement('script');
    script.src = 'src/features/按钮/书签管理/BookmarkHandler.js';
    script.async = false; // 确保同步加载
    document.head.appendChild(script);
}

// BookmarkHandler.js中的类名已经是BookmarkTool，不需要额外导出 