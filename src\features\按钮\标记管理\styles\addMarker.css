/* AddMarker 样式 */

/* 标记点弹窗样式 */
.addmarker-popup-titile {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    border-radius: 3px 3px 0 0;
    padding: 0 80px 0 20px;
    height: 42px;
    line-height: 42px;
    font-size: 16px;
    color: white;
    background-color: #20a0ff;
    border-bottom: 1px solid #eee;
    overflow: hidden;
}

.addmarker-popup-content {
    width: 260px;
    margin-top: 52px;
    font-size: 14px;
}

.form-group {
    color: #333333;
    margin-bottom: 15px;
}

/* 头部工具栏 */
.addmarker-panel {
    position: absolute;
    width: 300px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    z-index: 1000;
}

.addmarker-panel-header {
    line-height: 35px;
    border-bottom: #dedede 1px solid;
    overflow: hidden;
    background-color: #f7f7f7;
    padding: 0 10px;
}

.addmarker-panel-header ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.addmarker-panel-header ul li {
    padding: 0;
    float: left;
    display: inline-block;
    margin-right: 5px;
}

.addmarker-panel-header ul li button {
    width: 28px;
    height: 28px;
    line-height: 26px;
    text-align: center;
    background: #fff;
    border: #dedede 1px solid;
    color: #3374e0;
    display: inline-block;
    font-size: 14px;
    margin-left: 5px;
    cursor: pointer;
    border-radius: 3px;
    padding: 0;
}

.addmarker-panel-header ul li button:hover {
    background-color: #f0f0f0;
}

.addmarker-panel-header ul li button.active {
    background-color: #3374e0;
    color: #fff;
    border-color: #3374e0;
}

.addmarker-panel-body {
    position: absolute;
    top: 36px;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: auto;
}

/* 表格样式 */
.addmarker-table {
    width: 100%;
    border-collapse: collapse;
}

.addmarker-table th,
.addmarker-table td {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.addmarker-table th {
    background-color: #f7f7f7;
    font-weight: normal;
}

.addmarker-table tr:hover {
    background-color: #f0f0f0;
    cursor: pointer;
}

/* 暗色主题支持 */
.dark .form-group {
    color: #ffffff;
}

.dark .addmarker-popup-titile {
    background-color: rgba(29, 63, 70, 0.7);
    border-bottom: 1px solid #566f64;
}

.dark .addmarker-panel {
    background-color: rgba(40, 44, 52, 0.9);
    border: 1px solid #444;
}

.dark .addmarker-panel-header {
    background-color: #333;
    border-bottom: 1px solid #444;
}

.dark .addmarker-panel-header ul li button {
    background-color: #444;
    border-color: #555;
    color: #ddd;
}

.dark .addmarker-panel-header ul li button:hover {
    background-color: #555;
}

.dark .addmarker-panel-header ul li button.active {
    background-color: #3374e0;
    color: #fff;
}

.dark .addmarker-table th {
    background-color: #333;
    border-bottom: 1px solid #444;
}

.dark .addmarker-table td {
    border-bottom: 1px solid #444;
}

.dark .addmarker-table tr:hover {
    background-color: #444;
}

/* 工具栏按钮样式 - 与项目其他工具按钮一致 */
.tool-button.addmarker-btn {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    border: none;
    padding: 8px;
    margin: 5px;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
}

.tool-button.addmarker-btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tool-button.addmarker-btn svg {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
}

.tool-button.addmarker-btn span {
    font-size: 12px;
    color: #333;
}

.dark .tool-button.addmarker-btn {
    background-color: rgba(40, 44, 52, 0.8);
}

.dark .tool-button.addmarker-btn:hover {
    background-color: rgba(50, 54, 62, 0.9);
}

.dark .tool-button.addmarker-btn span {
    color: #ddd;
} 