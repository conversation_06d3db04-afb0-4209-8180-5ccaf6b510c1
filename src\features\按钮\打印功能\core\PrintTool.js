/**
 * 打印功能核心模块 - 纯Cesium实现，不依赖Mars3D
 */
// 检查Cesium全局对象是否存在
if (!window.Cesium) {
    console.error('Cesium全局对象未找到，打印功能可能无法正常工作');
}

class PrintTool {
    /**
     * 创建打印工具实例
     * @param {Object} viewer Cesium viewer对象 
     * @param {Object} options 配置选项
     */
    constructor(viewer, options = {}) {
        this.viewer = viewer;
        this.options = { ...PrintConfig.print, ...options };
        this.printStyleAdded = false;
        
        // 添加打印样式
        this._addPrintStyle();
    }
    
    /**
     * 添加打印样式到文档
     * @private
     */
    _addPrintStyle() {
        if (this.printStyleAdded) return;
        
        const style = document.createElement('style');
        style.id = 'cesium-print-style';
        style.innerHTML = `
            @media print {
                body * {
                    visibility: hidden;
                }
                #cesiumContainer, #cesiumContainer * {
                    visibility: visible;
                }
                #cesiumContainer {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                }
                
                ${PrintConfig.hiddenElementsInPrint.map(cls => `.${cls}`).join(', ')} {
                    display: none !important;
                }
                
                .print-only {
                    display: block !important;
                    visibility: visible !important;
                }
            }
        `;
        document.head.appendChild(style);
        this.printStyleAdded = true;
    }
    
    /**
     * 捕获当前视图为图片
     * @param {Object} options 截图选项
     * @returns {Promise<string>} 返回图片的dataURL
     */
    captureImage(options = {}) {
        const opts = { ...this.options, ...options };
        
        return new Promise((resolve, reject) => {
            try {
                // 强制渲染一帧
                this.viewer.render();
                
                // 获取canvas
                const canvas = this.viewer.canvas;
                
                // 转换为dataURL
                const dataUrl = canvas.toDataURL(opts.imageFormat, opts.imageQuality);
                resolve(dataUrl);
            } catch (error) {
                console.error('截图失败:', error);
                reject(error);
            }
        });
    }
    
    /**
     * 保存截图到本地文件
     * @param {Object} options 截图选项
     */
    async saveImage(options = {}) {
        try {
            const dataUrl = await this.captureImage(options);
            const filename = options.filename || this.options.defaultFilename;
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = `${filename}.png`;
            link.href = dataUrl;
            link.style.display = 'none';
            
            // 添加到文档并触发点击
            document.body.appendChild(link);
            link.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            }, 100);
        } catch (error) {
            console.error('保存图片失败:', error);
            throw error;
        }
    }
    
    /**
     * 打印当前视图
     * @param {Object} options 打印选项
     */
    print(options = {}) {
        const opts = { ...this.options, ...options };
        
        // 隐藏不需要打印的元素
        const hiddenElements = [];
        if (!opts.includeUI) {
            PrintConfig.hiddenElementsInPrint.forEach(className => {
                const elements = document.getElementsByClassName(className);
                for (let i = 0; i < elements.length; i++) {
                    const el = elements[i];
                    if (el.style.display !== 'none') {
                        el.dataset.originalDisplay = el.style.display;
                        el.style.display = 'none';
                        hiddenElements.push(el);
                    }
                }
            });
        }
        
        // 添加打印标题和图例(如果有)
        let titleEl = null;
        if (opts.title) {
            titleEl = document.createElement('div');
            titleEl.className = 'print-title print-only';
            titleEl.innerHTML = opts.title;
            titleEl.style.cssText = `
                position: absolute;
                top: 10px;
                left: 0;
                right: 0;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #333;
                z-index: 1000;
                display: none;
            `;
            document.body.appendChild(titleEl);
        }
        
        // 调用打印
        window.print();
        
        // 恢复隐藏的元素
        hiddenElements.forEach(el => {
            el.style.display = el.dataset.originalDisplay || '';
            delete el.dataset.originalDisplay;
        });
        
        // 移除添加的标题
        if (titleEl) {
            document.body.removeChild(titleEl);
        }
    }
    
    /**
     * 销毁实例，清理资源
     */
    destroy() {
        // 移除添加的样式
        const style = document.getElementById('cesium-print-style');
        if (style) {
            document.head.removeChild(style);
        }
        
        // 清除引用
        this.viewer = null;
        this.options = null;
    }
} 