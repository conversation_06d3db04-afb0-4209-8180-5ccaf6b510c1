/**
 * 3D建筑UI组件
 * 提供3D建筑功能的界面和事件处理
 */
class Building3DUI {
    constructor(viewer) {
        this.viewer = viewer;
        this.building3DTool = null;
        this.panelId = 'building3DPanel';
        this.toggleBtnId = 'toggleBuilding3D';
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const button = document.createElement('button');
        button.id = this.toggleBtnId;
        button.innerHTML = `
            <img src="src/features/按钮/三维建筑/svg/building.svg" alt="3D建筑">
            <div class="tooltip">3D建筑</div>
        `;
        
        container.appendChild(button);
        console.log(`3D建筑按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const panel = document.createElement('div');
        panel.id = this.panelId;
        panel.className = 'analysis-panel';
        panel.style.display = 'none';
        panel.innerHTML = `
            <div class="panel-title">3D建筑工具</div>
            <div class="building-controls">
                <div class="building-type-selector">
                    <label>建筑类型：</label>
                    <select id="buildingTypeSelect">
                        <option value="0">住宅楼</option>
                        <option value="1">办公楼</option>
                        <option value="2">商业楼</option>
                        <option value="3">工业厂房</option>
                    </select>
                </div>
                
                <div class="building-height-input">
                    <label>建筑高度：</label>
                    <input type="number" id="buildingHeight" value="30" min="10" max="200" step="5">
                    <span>米</span>
                </div>
                
                <div class="building-color-input">
                    <label>建筑颜色：</label>
                    <input type="color" id="buildingColor" value="#4A90E2">
                </div>
                
                <div class="button-group">
                    <button id="startDrawBuilding" class="building-btn primary">
                        开始绘制
                    </button>
                    <button id="clearAllBuildings" class="building-btn danger">
                        清除全部
                    </button>
                </div>
                
                <div class="building-info">
                    <p>点击"开始绘制"后，在地图上点击位置添加建筑</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        console.log('3D建筑面板已添加到页面');
    }

    /**
     * 初始化3D建筑组件及事件绑定
     */
    init() {
        console.log('正在初始化3D建筑UI组件...');
        
        // 创建3D建筑工具实例
        if (!this.building3DTool) {
            this.building3DTool = new Building3DTool(this.viewer);
            window.building3DTool = this.building3DTool;
        }
        
        // 绑定按钮事件
        this._bindButtonEvents();
        
        // 绑定面板事件
        this._bindPanelEvents();
        
        console.log('3D建筑UI组件初始化完成');
    }

    /**
     * 绑定按钮事件
     * @private
     */
    _bindButtonEvents() {
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            toggleBtn.addEventListener('click', (e) => {
                console.log('3D建筑按钮被点击');
                this._togglePanel(toggleBtn);
            });
        }
    }

    /**
     * 绑定面板事件
     * @private
     */
    _bindPanelEvents() {
        // 建筑类型选择
        const typeSelect = document.getElementById('buildingTypeSelect');
        if (typeSelect) {
            typeSelect.addEventListener('change', (e) => {
                this._updateBuildingType();
            });
        }

        // 高度输入
        const heightInput = document.getElementById('buildingHeight');
        if (heightInput) {
            heightInput.addEventListener('input', (e) => {
                this._updateBuildingType();
            });
        }

        // 颜色输入
        const colorInput = document.getElementById('buildingColor');
        if (colorInput) {
            colorInput.addEventListener('change', (e) => {
                this._updateBuildingType();
            });
        }

        // 开始绘制按钮
        const startDrawBtn = document.getElementById('startDrawBuilding');
        if (startDrawBtn) {
            startDrawBtn.addEventListener('click', (e) => {
                this._startDrawBuilding();
            });
        }

        // 清除全部按钮
        const clearBtn = document.getElementById('clearAllBuildings');
        if (clearBtn) {
            clearBtn.addEventListener('click', (e) => {
                this._clearAllBuildings();
            });
        }
    }

    /**
     * 切换面板显示状态
     * @param {HTMLElement} button - 触发按钮
     * @private
     */
    _togglePanel(button) {
        const panel = document.getElementById(this.panelId);
        if (!panel) return;

        // 隐藏其他面板
        this._hideOtherPanels();

        if (panel.style.display === 'none' || !panel.style.display) {
            this.showPanel(button);
            this.building3DTool.activate();
        } else {
            this.hidePanel();
            this.building3DTool.deactivate();
        }
    }

    /**
     * 隐藏其他面板
     * @private
     */
    _hideOtherPanels() {
        const otherPanels = [
            'searchPanel', 'terrainDigPanel', 'measureToolContainer', 
            'profileAnalysisPanel', 'bookmarkPanel'
        ];
        otherPanels.forEach(panelId => {
            const panel = document.getElementById(panelId);
            if (panel) {
                panel.style.display = 'none';
            }
        });
    }

    /**
     * 更新建筑类型
     * @private
     */
    _updateBuildingType() {
        const typeSelect = document.getElementById('buildingTypeSelect');
        const heightInput = document.getElementById('buildingHeight');
        const colorInput = document.getElementById('buildingColor');

        if (!typeSelect || !heightInput || !colorInput) return;

        const buildingTypes = [
            { name: '住宅楼', height: 30, color: '#4A90E2' },
            { name: '办公楼', height: 80, color: '#7ED321' },
            { name: '商业楼', height: 50, color: '#F5A623' },
            { name: '工业厂房', height: 20, color: '#9013FE' }
        ];

        const selectedIndex = parseInt(typeSelect.value);
        const selectedType = buildingTypes[selectedIndex];

        // 更新当前建筑类型
        this.building3DTool.currentBuildingType = {
            name: selectedType.name,
            height: parseInt(heightInput.value),
            color: colorInput.value
        };
    }

    /**
     * 开始绘制建筑
     * @private
     */
    _startDrawBuilding() {
        console.log('开始绘制3D建筑');
        this._updateBuildingType();
        this.building3DTool.startDraw();
        
        // 更新按钮状态
        const startBtn = document.getElementById('startDrawBuilding');
        if (startBtn) {
            startBtn.textContent = '点击地图添加建筑';
            startBtn.classList.add('active');
        }

        // 显示提示信息
        const infoDiv = document.querySelector('.building-info p');
        if (infoDiv) {
            infoDiv.textContent = '请在地图上点击位置添加建筑，右键取消绘制';
        }
    }

    /**
     * 清除所有建筑
     * @private
     */
    _clearAllBuildings() {
        if (confirm('确定要清除所有3D建筑吗？')) {
            console.log('清除所有3D建筑');
            this.building3DTool.clearBuildings();
            
            // 重置按钮状态
            const startBtn = document.getElementById('startDrawBuilding');
            if (startBtn) {
                startBtn.textContent = '开始绘制';
                startBtn.classList.remove('active');
            }

            // 重置提示信息
            const infoDiv = document.querySelector('.building-info p');
            if (infoDiv) {
                infoDiv.textContent = '点击"开始绘制"后，在地图上点击位置添加建筑';
            }
        }
    }

    /**
     * 显示面板并设置位置
     * @param {HTMLElement} button - 触发按钮
     */
    showPanel(button) {
        const panel = document.getElementById(this.panelId);
        if (!panel) return;

        panel.style.display = 'block';

        // 使用PanelPositioner设置面板位置
        if (button && typeof PanelPositioner !== 'undefined') {
            try {
                PanelPositioner.setPosition(button, panel, {
                    preferredPosition: 'left',
                    gap: 10
                });
            } catch (e) {
                console.error('面板定位出错:', e);
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.right = '80px';
            }
        } else {
            // 默认位置
            panel.style.position = 'fixed';
            panel.style.top = '100px';
            panel.style.right = '80px';
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        const panel = document.getElementById(this.panelId);
        if (panel) {
            panel.style.display = 'none';
        }

        // 重置按钮状态
        const startBtn = document.getElementById('startDrawBuilding');
        if (startBtn) {
            startBtn.textContent = '开始绘制';
            startBtn.classList.remove('active');
        }
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {Building3DUI} 3D建筑UI实例
     */
    static init(viewer, toolButtonsId = 'toolButtons') {
        console.log('Building3DUI.init 被调用');
        const building3DUI = new Building3DUI(viewer);
        
        // 检查是否需要添加HTML元素
        if (!document.getElementById(building3DUI.toggleBtnId)) {
            building3DUI.appendButtonTo(toolButtonsId);
        }
        
        if (!document.getElementById(building3DUI.panelId)) {
            building3DUI.appendPanelToBody();
        }
        
        // 初始化组件
        building3DUI.init();
        
        return building3DUI;
    }
}

// 导出到全局作用域
window.Building3DUI = Building3DUI; 