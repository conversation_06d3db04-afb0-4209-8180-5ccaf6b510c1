/**
 * RoamFly核心功能模块 - 使用原生Cesium实现
 */
import { RoamFlyConfig } from '../config.js';

// 确保Cesium全局对象存在
const Cesium = window.Cesium;
if (!Cesium) {
    console.error('Cesium全局对象未找到，路线漫游功能可能无法正常工作');
}

export class RoamFlyTool {
    constructor(viewer, options = {}) {
        this.viewer = viewer;
        this.options = { ...RoamFlyConfig.roam, ...options };
        this.polyline = null;
        this.positions = [];
        this.isDrawing = false;
        this.drawHandler = null;
        this.handler = null;
        this.tempPositions = [];
        this.flyEntity = null;
        this.startTime = null;
        this.stopTime = null;
        this.timetik = null;
        this.speed = this.options.defaultSpeed; // km/h
        this.isFlying = false;
        this.timeline = null;
        this.timelineContainer = null;
        
        // 添加时间轴相关DOM
        this._setupTimeline();
    }

    /**
     * 将km/h转换为m/s
     */
    _kmhToMs(kmh) {
        return kmh * 1000 / 3600;
    }

    /**
     * 设置时间轴
     */
    _setupTimeline() {
        // 创建时间轴容器
        this.timelineContainer = document.createElement('div');
        this.timelineContainer.className = 'roamfly-timeline-container';
        this.timelineContainer.style.cssText = `
            position: absolute;
            bottom: 80px; 
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 50%; /* 减小宽度，避免遮挡面板 */
            max-width: 800px; /* 限制最大宽度 */
            height: 40px;
            background: rgba(38, 38, 38, 0.8);
            border-radius: 4px;
            display: none;
            z-index: 9999;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        `;
        
        // 创建时间轴内容
        this.timelineContainer.innerHTML = `
            <div class="timeline-controls" style="display: flex; align-items: center; height: 100%;">
                <button id="playPauseBtn" class="timeline-btn" style="background: none; border: none; color: white; cursor: pointer; margin-right: 10px; padding: 0;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </button>
                <div class="timeline-track" style="flex-grow: 1; height: 6px; background: rgba(255,255,255,0.2); border-radius: 3px; position: relative;">
                    <div id="timelineProgress" class="timeline-progress" style="position: absolute; height: 100%; background: #00a8ff; border-radius: 3px; width: 0%;"></div>
                    <div id="timelineScrubber" class="timeline-scrubber" style="position: absolute; width: 14px; height: 14px; background: #fff; border-radius: 50%; top: 50%; transform: translate(-50%, -50%); left: 0%; cursor: pointer;"></div>
                </div>
                <div id="timelineTime" class="timeline-time" style="margin-left: 10px; color: white; font-size: 12px; min-width: 60px; text-align: right;">00:00</div>
            </div>
        `;
        
        document.body.appendChild(this.timelineContainer);
        
        // 添加事件监听
        this._setupTimelineEvents();
    }
    
    /**
     * 设置时间轴事件
     */
    _setupTimelineEvents() {
        const playPauseBtn = document.getElementById('playPauseBtn');
        const timelineScrubber = document.getElementById('timelineScrubber');
        const timelineTrack = document.querySelector('.timeline-track');
        
        if (playPauseBtn) {
            playPauseBtn.addEventListener('click', () => {
                this._togglePlayPause();
            });
        }
        
        if (timelineScrubber && timelineTrack) {
            let isDragging = false;
            
            timelineScrubber.addEventListener('mousedown', () => {
                isDragging = true;
                // 暂停动画
                if (this._animationData) {
                    this._animationData.isPaused = true;
                }
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging && this.startTime && this.stopTime) {
                    const trackRect = timelineTrack.getBoundingClientRect();
                    let percentage = (e.clientX - trackRect.left) / trackRect.width;
                    percentage = Math.max(0, Math.min(percentage, 1));
                    
                    // 更新时间轴位置
                    this._updateTimelineScrubber(percentage);
                    
                    // 设置当前百分比
                    if (this._animationData) {
                        this._animationData.currentPercentage = percentage;
                        
                        // 计算对应的当前时间
                        const totalDiff = Cesium.JulianDate.secondsDifference(this.stopTime, this.startTime);
                        const newTime = Cesium.JulianDate.addSeconds(
                            this.startTime, 
                            percentage * totalDiff, 
                            new Cesium.JulianDate()
                        );
                        
                        // 更新实体位置
                        this.viewer.clock.currentTime = newTime;
                        this.viewer.scene.requestRender();
                        
                        // 更新实时信息
                        this._updateRealTimeInfo(percentage);
                    }
                }
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging && this._animationData) {
                    isDragging = false;
                    
                    // 从当前位置继续播放
                    const percentage = this._animationData.currentPercentage;
                    const now = Date.now();
                    
                    // 重置动画参数
                    this._animationData.startTime = now - (percentage * this._animationData.totalDuration);
                    this._animationData.pausedTime = 0;
                    
                    // 如果刚才是暂停状态，保持暂停
                    if (this._animationData.isPaused) {
                        this._animationData.pauseStart = now;
                    } else {
                        // 否则恢复播放
                        this._animationData.isPaused = false;
                    }
                }
            });
            
            timelineTrack.addEventListener('click', (e) => {
                if (this.startTime && this.stopTime && this._animationData) {
                    const trackRect = timelineTrack.getBoundingClientRect();
                    let percentage = (e.clientX - trackRect.left) / trackRect.width;
                    percentage = Math.max(0, Math.min(percentage, 1));
                    
                    // 更新时间轴位置
                    this._updateTimelineScrubber(percentage);
                    
                    // 从点击位置继续播放
                    const now = Date.now();
                    this._animationData.startTime = now - (percentage * this._animationData.totalDuration);
                    this._animationData.pausedTime = 0;
                    
                    // 如果刚才是暂停状态，保持暂停
                    if (this._animationData.isPaused) {
                        this._animationData.pauseStart = now;
                    }
                    
                    // 计算对应的当前时间
                    const totalDiff = Cesium.JulianDate.secondsDifference(this.stopTime, this.startTime);
                    const newTime = Cesium.JulianDate.addSeconds(
                        this.startTime, 
                        percentage * totalDiff, 
                        new Cesium.JulianDate()
                    );
                    
                    // 更新实体位置
                    this.viewer.clock.currentTime = newTime;
                    this.viewer.scene.requestRender();
                    
                    // 更新实时信息
                    this._updateRealTimeInfo(percentage);
                }
            });
        }
    }
    
    /**
     * 切换播放/暂停状态
     */
    _togglePlayPause() {
        if (!this.isFlying || !this._animationData) return;
        
        const data = this._animationData;
        
        // 查找播放/暂停按钮，优先在时间轴容器中查找，然后在整个文档中查找
        let playPauseBtn = null;
        if (this.timelineContainer) {
            playPauseBtn = this.timelineContainer.querySelector('#playPauseBtn');
        }
        if (!playPauseBtn) {
            playPauseBtn = document.getElementById('playPauseBtn');
        }
        
        if (data.isPaused) {
            // 取消暂停
            data.isPaused = false;
            // 计算已暂停的总时间
            data.pausedTime += (Date.now() - data.pauseStart);
            
            // 更新按钮状态
            if (playPauseBtn) {
                playPauseBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    </svg>
                `;
            }
        } else {
            // 暂停
            data.isPaused = true;
            data.pauseStart = Date.now();
            
            // 更新按钮状态
            if (playPauseBtn) {
                playPauseBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                `;
            }
        }
    }
    
    /**
     * 更新时间轴位置
     */
    _updateTimelineScrubber(percentage) {
        const timelineProgress = document.getElementById('timelineProgress');
        const timelineScrubber = document.getElementById('timelineScrubber');
        const timelineTime = document.getElementById('timelineTime');
        
        if (timelineProgress && timelineScrubber && timelineTime) {
            timelineProgress.style.width = `${percentage * 100}%`;
            timelineScrubber.style.left = `${percentage * 100}%`;
            
            // 计算并显示时间
            if (this.startTime && this.stopTime) {
                const totalDuration = Cesium.JulianDate.secondsDifference(this.stopTime, this.startTime);
                const currentDuration = totalDuration * percentage;
                
                const minutes = Math.floor(currentDuration / 60);
                const seconds = Math.floor(currentDuration % 60);
                
                timelineTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }
    }
    
    /**
     * 显示时间轴
     */
    _showTimeline() {
        if (this.timelineContainer) {
            this.timelineContainer.style.display = 'block';
        }
    }
    
    /**
     * 隐藏时间轴
     */
    _hideTimeline() {
        if (this.timelineContainer) {
            this.timelineContainer.style.display = 'none';
        }
    }

    /**
     * 开始绘制路线
     */
    startDrawing() {
        if (this.isDrawing) return;
        
        this.isDrawing = true;
        this.tempPositions = [];
        
        // 创建临时折线
        this.polyline = this.viewer.entities.add({
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return this.tempPositions;
                }, false),
                width: 3,
                material: new Cesium.ColorMaterialProperty(
                    Cesium.Color.YELLOW.withAlpha(0.5)
                )
            }
        });

        // 设置鼠标事件处理
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        
        // 左键点击添加点
        this.handler.setInputAction((event) => {
            const position = this.viewer.scene.pickPosition(event.position);
            if (position) {
                this.tempPositions.push(position);
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        // 鼠标移动时更新临时点
        this.handler.setInputAction((event) => {
            if (this.tempPositions.length > 0) {
                const position = this.viewer.scene.pickPosition(event.endPosition);
                if (position) {
                    if (this.tempPositions.length > 1) {
                        this.tempPositions.pop();
                    }
                    this.tempPositions.push(position);
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        // 右键完成绘制
        this.handler.setInputAction(() => {
            this.stopDrawing();
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }

    /**
     * 停止绘制
     */
    stopDrawing() {
        if (!this.isDrawing) return;

        this.isDrawing = false;
        
        // 移除事件处理器
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }

        // 保存最终位置
        this.positions = [...this.tempPositions];
        if (this.positions.length >= 2) {
            // 初始化漫游路线
            this.initialize(this.positions);

            // 触发路线创建完成事件
            const event = new CustomEvent('roamFlyRouteCreated', {
                detail: {
                    positions: this.positions,
                    length: this.calculateLength()
                }
            });
            document.dispatchEvent(event);
        }

        // 清理临时绘制状态
        this.tempPositions = [];
        if (this.polyline) {
            this.viewer.entities.remove(this.polyline);
            this.polyline = null;
        }
    }

    /**
     * 计算路线长度（米）
     */
    calculateLength() {
        let length = 0;
        for (let i = 0; i < this.positions.length - 1; i++) {
            length += Cesium.Cartesian3.distance(this.positions[i], this.positions[i + 1]);
        }
        return length;
    }

    /**
     * 初始化漫游
     */
    initialize(positions) {
        if (!positions || positions.length < 2) {
            throw new Error('路线无坐标数据，无法漫游！');
        }

        // 确保所有点都有高度
        const heights = positions.map(pos => {
            const cart = Cesium.Cartographic.fromCartesian(pos);
            return cart.height;
        });
        
        // 使用与_createSampledPoints方法相同的计算逻辑
        // 使用路线点的平均高度作为基础高度
        const avgHeight = heights.reduce((sum, h) => sum + h, 0) / heights.length;
        
        // 必要的最小高度偏移，确保飞机在地表上方
        const MIN_HEIGHT_OFFSET = 200;
        
        // 应用高度系数
        const heightFactor = this.options.heightFactor || 1.5;
        
        // 计算最终飞行高度 = 最大值(地形平均高度, 默认高度) + 额外偏移 * 高度系数
        const flyHeight = Math.max(
            avgHeight,
            this.options.defaultHeight || 500
        ) + (MIN_HEIGHT_OFFSET * heightFactor);
        
        console.log('路线平均高度:', avgHeight.toFixed(2) + 'm');
        console.log('最终飞行高度:', flyHeight.toFixed(2) + 'm');

        // 使用统一高度创建路线点
        const heightPositions = positions.map(pos => {
            const cart = Cesium.Cartographic.fromCartesian(pos);
            return Cesium.Cartesian3.fromRadians(
                cart.longitude,
                cart.latitude,
                flyHeight
            );
        });

        // 创建路线实体
        if (this.polyline) {
            this.viewer.entities.remove(this.polyline);
        }
        
        this.polyline = this.viewer.entities.add({
            polyline: {
                positions: heightPositions,
                width: 3,
                material: new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW),
                clampToGround: false,
                heightReference: Cesium.HeightReference.NONE
            }
        });

        // 保存带统一高度的位置
        this.positions = heightPositions;

        // 计算飞行时间（基于km/h速度）
        const speedMs = this._kmhToMs(this.speed);
        const totalSeconds = this.calculateLength() / speedMs;

        // 触发初始化完成事件
        const event = new CustomEvent('roamFlyRouteInitialized', {
            detail: {
                name: '新建路线',
                alllen: this.calculateLength(),
                alltime: totalSeconds
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 开始漫游
     */
    start() {
        if (!this.positions || this.positions.length < 2) {
            throw new Error('请先绘制漫游路线！');
        }

        // 停止之前的漫游
        this.stop();
        
        try {
            console.log('开始漫游, 路线点数:', this.positions.length);
            
            // 显示时间轴
            this._showTimeline();
            
            // 计算飞行时间
            const speedMs = this._kmhToMs(this.speed); // 转换为米/秒
            const totalSeconds = this.calculateLength() / speedMs;
            
            console.log('飞行参数:', {
                distance: this.calculateLength().toFixed(2) + 'm',
                speed: this.speed + 'km/h (' + speedMs.toFixed(2) + 'm/s)',
                time: totalSeconds.toFixed(2) + 's'
            });
            
            // 使用当前日期作为起点
            this.startTime = Cesium.JulianDate.fromDate(new Date());
            this.stopTime = Cesium.JulianDate.addSeconds(
                this.startTime,
                totalSeconds,
                new Cesium.JulianDate()
            );
            
            // 创建位置属性
            const position = new Cesium.SampledPositionProperty();
            
            // 创建采样点
            const sampledPoints = this._createSampledPoints(this.positions, totalSeconds);
            console.log('创建采样点:', sampledPoints.length);
            
            // 添加采样点
            sampledPoints.forEach(point => {
                position.addSample(point.time, point.position);
            });
            
            // ========== 新增代码：更新路线显示 ==========
            // 从采样点中提取实际飞行路径的位置点
            const flightPathPositions = sampledPoints.map(point => point.position);
            
            // 移除旧的路线
            if (this.polyline) {
                this.viewer.entities.remove(this.polyline);
                this.polyline = null;
            }
            
            // 使用实际飞行路径创建新的路线
            this.polyline = this.viewer.entities.add({
                polyline: {
                    positions: flightPathPositions,
                    width: 3,
                    material: new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW),
                    clampToGround: false,
                    heightReference: Cesium.HeightReference.NONE
                }
            });
            
            console.log('已更新路线显示为实际飞行路径，共', flightPathPositions.length, '个点');
            
            // 确保模型URL存在
            const modelUrl = this.options.modelUrl || 'src/models/zhanji.glb';
            console.log('使用飞行模型:', modelUrl);
            
            // 创建飞行实体
            this.flyEntity = this.viewer.entities.add({
                id: "roamFly_entity",
                availability: new Cesium.TimeIntervalCollection([
                    new Cesium.TimeInterval({
                        start: this.startTime,
                        stop: this.stopTime
                    })
                ]),
                position: position,
                // 使用简单朝向设置
                orientation: new Cesium.VelocityOrientationProperty(position),
                model: {
                    uri: modelUrl,
                    minimumPixelSize: 64,
                    maximumScale: 128,
                    scale: 0.1,
                    // 不添加可能导致问题的属性
                    heightReference: Cesium.HeightReference.NONE
                },
                path: {
                    resolution: 1,
                    material: new Cesium.PolylineGlowMaterialProperty({
                        glowPower: 0.1,
                        color: Cesium.Color.YELLOW
                    }),
                    width: 8,
                    leadTime: 0
                }
            });
            
            // 根据视角模式设置相机行为
            this._applyViewMode();
            
            // 设置状态
            this.isFlying = true;
            
            // 更新播放按钮状态
            let playPauseBtn = null;
            if (this.timelineContainer) {
                playPauseBtn = this.timelineContainer.querySelector('#playPauseBtn');
            }
            if (!playPauseBtn) {
                playPauseBtn = document.getElementById('playPauseBtn');
            }
            
            if (playPauseBtn) {
                playPauseBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    </svg>
                `;
            }
            
            // 重要：使用自定义动画系统替代Cesium时钟
            this._setupAnimation(totalSeconds);
            
            // 日志记录
            console.log('漫游启动完成');
            
            return true;

        } catch (error) {
            console.error('漫游启动失败:', error);
            this.stop();
            return false;
        }
    }
    
    /**
     * 根据当前视角模式设置相机
     * 模式1: 跟随视角 - 相机跟随飞机，显示飞机和前方路线
     * 模式2: 锁定视角 - 相机锁定在飞机上，显示第一人称视角
     * 模式3: 自由视角 - 相机不跟随飞机，可以自由控制
     */
    _applyViewMode() {
        if (!this.flyEntity) return;
        
        // 获取当前视角模式
        const viewMode = this.options.viewMode || 1;
        console.log('应用视角模式:', viewMode);
        
        // 获取视角距离
        const cameraDistance = this.options.cameraDistance || 200;
        
        // 重置相机行为
        this.viewer.trackedEntity = undefined;
        this.viewer.scene.screenSpaceCameraController.enableRotate = true;
        this.viewer.scene.screenSpaceCameraController.enableTranslate = true;
        this.viewer.scene.screenSpaceCameraController.enableZoom = true;
        this.viewer.scene.screenSpaceCameraController.enableTilt = true;
        
        switch (Number(viewMode)) {
            case 1: // 跟随视角
                this.viewer.trackedEntity = this.flyEntity;
                
                // 设置飞机后上方的视角
                const offset = new Cesium.HeadingPitchRange(
                    0,                           // 朝向
                    Cesium.Math.toRadians(-30),  // 俯仰角
                    cameraDistance                // 距离
                );
                
                // 延迟一帧以确保正确应用
                setTimeout(() => {
                    if (this.viewer && this.flyEntity && this.isFlying) {
                        this.viewer.zoomTo(this.flyEntity, offset);
                    }
                }, 100);
                
                break;
                
            case 2: // 锁定视角 (第一人称)
                this.viewer.trackedEntity = this.flyEntity;
                
                // 设置第一人称视角 (正前方略向下)
                const firstPersonOffset = new Cesium.HeadingPitchRange(
                    0,                           // 朝向
                    Cesium.Math.toRadians(-5),   // 略微向下的角度
                    10                           // 非常靠近
                );
                
                // 延迟一帧以确保正确应用
                setTimeout(() => {
                    if (this.viewer && this.flyEntity && this.isFlying) {
                        this.viewer.zoomTo(this.flyEntity, firstPersonOffset);
                        
                        // 禁用用户对相机的控制
                        this.viewer.scene.screenSpaceCameraController.enableRotate = false;
                        this.viewer.scene.screenSpaceCameraController.enableTranslate = false;
                        this.viewer.scene.screenSpaceCameraController.enableZoom = false;
                        this.viewer.scene.screenSpaceCameraController.enableTilt = false;
                    }
                }, 100);
                
                break;
                
            case 3: // 自由视角
                // 不跟踪实体
                this.viewer.trackedEntity = undefined;
                
                // 初始化一个俯视飞行路径的视角
                const polylinePositions = this.polyline.polyline.positions.getValue();
                if (polylinePositions && polylinePositions.length > 0) {
                    // 计算路线的中心点
                    const boundingSphere = Cesium.BoundingSphere.fromPoints(polylinePositions);
                    
                    // 设置相机位置为上方俯瞰
                    const offset = new Cesium.HeadingPitchRange(
                        0,                            // 朝向
                        Cesium.Math.toRadians(-45),   // 45度俯视角
                        boundingSphere.radius * 2     // 距离为路线半径的2倍
                    );
                    
                    setTimeout(() => {
                        if (this.viewer && this.isFlying) {
                            this.viewer.zoomTo(new Cesium.Entity({
                                position: boundingSphere.center
                            }), offset);
                        }
                    }, 100);
                }
                
                break;
        }
    }
    
    /**
     * 设置自定义动画系统
     */
    _setupAnimation(totalSeconds) {
        // 清除可能存在的动画帧
        if (this._animationFrameId) {
            cancelAnimationFrame(this._animationFrameId);
            this._animationFrameId = null;
        }
        
        // 初始化动画参数
        this._animationData = {
            startTime: Date.now(),
            totalDuration: totalSeconds * 1000, // 转为毫秒
            isPaused: false,
            pausedTime: 0,
            pauseStart: 0,
            currentPercentage: 0,
            lastUpdateTime: Date.now(),
            debugCounter: 0  // 添加调试计数器
        };
        
        // 立即使用第一帧
        this._updateFrame(0);
        
        // 绑定动画帧函数到当前实例
        this._boundAnimationTick = this._animationTick.bind(this);
        
        // 开始动画循环
        this._animationFrameId = requestAnimationFrame(this._boundAnimationTick);
        
        console.log('动画系统设置完成', {
            startTime: new Date(this._animationData.startTime).toISOString(),
            totalDuration: (this._animationData.totalDuration / 1000).toFixed(2) + 's',
            animationId: this._animationFrameId
        });
    }
    
    /**
     * 动画帧函数
     */
    _animationTick() {
        // 重要：如果这里返回了，动画就会停止
        if (!this.isFlying) {
            console.warn('动画停止: isFlying = false');
            return;
        }
        
        if (!this._animationData) {
            console.warn('动画停止: 没有动画数据');
            return;
        }
        
        try {
            const now = Date.now();
            const data = this._animationData;
            
            // 调试信息
            data.debugCounter++;
            if (data.debugCounter % 60 === 0) { // 每60帧输出一次
                console.log(`动画帧 #${data.debugCounter}`, {
                    now: new Date(now).toISOString().substring(11, 23),
                    isPaused: data.isPaused,
                    animationId: this._animationFrameId
                });
            }
            
            // 如果暂停，不更新进度
            if (data.isPaused) {
                this._animationFrameId = requestAnimationFrame(this._boundAnimationTick);
                return;
            }
            
            // 计算当前经过的时间（考虑暂停的时间）
            const elapsedTime = now - data.startTime - data.pausedTime;
            
            // 计算进度百分比
            let percentage = elapsedTime / data.totalDuration;
            
            // 循环播放处理
            if (percentage >= 1) {
                // 重置动画
                data.startTime = now;
                data.pausedTime = 0;
                percentage = 0;
                console.log('动画循环重置');
            }
            
            // 更新当前帧
            this._updateFrame(percentage);
            
            // 每5秒记录一次状态
            if (now - data.lastUpdateTime > 5000) {
                data.lastUpdateTime = now;
                console.log('动画状态:', {
                    percentage: (percentage * 100).toFixed(2) + '%',
                    elapsedTime: (elapsedTime / 1000).toFixed(2) + 's',
                    totalDuration: (data.totalDuration / 1000).toFixed(2) + 's',
                    isPaused: data.isPaused
                });
            }
            
            // 继续下一帧 - 确保这是函数中的最后一步
            this._animationFrameId = requestAnimationFrame(this._boundAnimationTick);
            
        } catch (error) {
            console.error('动画帧处理错误:', error);
            // 出错但仍继续动画
            this._animationFrameId = requestAnimationFrame(this._boundAnimationTick);
        }
    }
    
    /**
     * 更新单帧
     */
    _updateFrame(percentage) {
        if (!this._animationData) return;
        
        // 保存当前百分比
        this._animationData.currentPercentage = percentage;
        
        // 根据百分比计算当前时间点
        const currentTime = Cesium.JulianDate.addSeconds(
            this.startTime,
            percentage * Cesium.JulianDate.secondsDifference(this.stopTime, this.startTime),
            new Cesium.JulianDate()
        );
        
        // 更新时间轴位置
        this._updateTimelineScrubber(percentage);
        
        // 直接更新实体位置，绕过时钟
        if (this.flyEntity && this.flyEntity.position) {
            // 更新Cesium时钟和强制渲染
            this.viewer.clock.currentTime = currentTime;
            
            // 强制更新和渲染
            this.viewer.scene.requestRender();
        }
        
        // 更新实时信息
        this._updateRealTimeInfo(percentage);
        
        // 更新已飞行路径显示 (每5%更新一次，减少性能消耗)
        if (percentage > 0 && Math.abs(percentage - (this._lastPathUpdatePercentage || 0)) > 0.05) {
            this._updateFlightPathDisplay(percentage);
            this._lastPathUpdatePercentage = percentage;
        }
    }
    
    /**
     * 更新飞行路径显示，区分已飞行和未飞行部分
     * @param {Number} percentage 当前飞行进度百分比
     */
    _updateFlightPathDisplay(percentage) {
        // 如果正在飞行且有飞行实体
        if (!this.isFlying || !this.flyEntity || !this.polyline) return;
        
        try {
            // 获取路线上的总点数
            const allPositions = this.polyline.polyline.positions.getValue();
            if (!allPositions || allPositions.length < 2) return;
            
            // 计算当前应该到达的点的索引
            const currentIndex = Math.floor(percentage * (allPositions.length - 1));
            
            // 至少保证有一个点被标记为已飞行
            const safeIndex = Math.max(1, currentIndex);
            
            // 创建已飞行和未飞行的点数组
            const flownPositions = allPositions.slice(0, safeIndex + 1);
            const remainingPositions = allPositions.slice(safeIndex);
            
            // 确保两段有重叠点，避免出现间隙
            if (flownPositions.length > 0 && remainingPositions.length > 0) {
                // 移除已有的动态路线显示
                if (this._flownPathEntity) {
                    this.viewer.entities.remove(this._flownPathEntity);
                }
                if (this._remainingPathEntity) {
                    this.viewer.entities.remove(this._remainingPathEntity);
                }
                
                // 创建已飞行部分 (亮黄色)
                if (flownPositions.length >= 2) {
                    this._flownPathEntity = this.viewer.entities.add({
                        polyline: {
                            positions: flownPositions,
                            width: 4,
                            material: new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(1.0)),
                            clampToGround: false
                        }
                    });
                }
                
                // 创建未飞行部分 (透明黄色)
                if (remainingPositions.length >= 2) {
                    this._remainingPathEntity = this.viewer.entities.add({
                        polyline: {
                            positions: remainingPositions,
                            width: 3,
                            material: new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(0.3)),
                            clampToGround: false
                        }
                    });
                }
                
                // 隐藏原始路径，使用新的分段路径替代
                this.polyline.polyline.show = false;
            }
        } catch (error) {
            console.error('更新飞行路径显示错误:', error);
        }
    }
    
    /**
     * 创建采样点
     * @param {Array} positions 路线位置点
     * @param {Number} totalSeconds 飞行总时间
     * @param {Cesium.JulianDate} [startTimeOverride] 可选的起始时间覆盖
     * @returns {Array} 采样点数组
     */
    _createSampledPoints(positions, totalSeconds, startTimeOverride) {
        const result = [];
        const actualStartTime = startTimeOverride || this.startTime;
        
        // 计算路径总长度
        const totalLength = this.calculateLength();
        
        // 计算累积长度和比例
        const cumulativeDistances = [0];
        let currentDistance = 0;
        
        for (let i = 1; i < positions.length; i++) {
            const segmentDistance = Cesium.Cartesian3.distance(positions[i-1], positions[i]);
            currentDistance += segmentDistance;
            cumulativeDistances.push(currentDistance);
        }
        
        // 提取所有点的高度并计算平均高度作为飞行高度
        const heights = positions.map(pos => {
            const cart = Cesium.Cartographic.fromCartesian(pos);
            return cart.height;
        });
        
        // 使用路线点的平均高度作为基础高度
        // 确保高度不低于配置的默认高度
        const avgHeight = heights.reduce((sum, h) => sum + h, 0) / heights.length;
        
        // 必要的最小高度偏移，确保飞机在地表上方
        const MIN_HEIGHT_OFFSET = 200;
        
        // 应用高度系数
        const heightFactor = this.options.heightFactor || 1.5;
        
        // 计算最终飞行高度 = 最大值(地形平均高度, 默认高度) + 额外偏移 * 高度系数
        const flyHeight = Math.max(
            avgHeight,
            this.options.defaultHeight || 500
        ) + (MIN_HEIGHT_OFFSET * heightFactor);
        
        console.log('飞行高度设置:', {
            avgTerrainHeight: avgHeight.toFixed(2) + 'm',
            configuredHeight: (this.options.defaultHeight || 500) + 'm',
            heightFactor: heightFactor,
            heightOffset: MIN_HEIGHT_OFFSET + 'm',
            finalFlyHeight: flyHeight.toFixed(2) + 'm'
        });
        
        // 创建更多的样本点以确保平滑
        const numSamples = Math.max(100, positions.length * 10);
        
        for (let i = 0; i <= numSamples; i++) {
            const fraction = i / numSamples;
            const targetDistance = fraction * totalLength;
            
            // 找到对应的段
            let segmentIndex = 0;
            while (segmentIndex < cumulativeDistances.length - 1 && 
                   cumulativeDistances[segmentIndex + 1] < targetDistance) {
                segmentIndex++;
            }
            
            // 计算该段内的位置比例
            let segmentFraction = 0;
            if (segmentIndex < cumulativeDistances.length - 1) {
                const segmentStart = cumulativeDistances[segmentIndex];
                const segmentEnd = cumulativeDistances[segmentIndex + 1];
                const segmentLength = segmentEnd - segmentStart;
                if (segmentLength > 0) {
                    segmentFraction = (targetDistance - segmentStart) / segmentLength;
                }
            }
            
            let position;
            
            // 处理最后一个点的特殊情况
            if (segmentIndex >= positions.length - 1) {
                // 使用最后一个点的经纬度，但保持统一高度
                const cartesian = positions[positions.length - 1];
                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                position = Cesium.Cartesian3.fromRadians(
                    cartographic.longitude,
                    cartographic.latitude,
                    flyHeight
                );
            } else {
                // 获取当前段的起点和终点
                const start = positions[segmentIndex];
                const end = positions[segmentIndex + 1];
                
                // 获取经纬度
                const startCart = Cesium.Cartographic.fromCartesian(start);
                const endCart = Cesium.Cartographic.fromCartesian(end);
                
                // 在球面上进行经纬度插值
                const lerpLon = Cesium.Math.lerp(startCart.longitude, endCart.longitude, segmentFraction);
                const lerpLat = Cesium.Math.lerp(startCart.latitude, endCart.latitude, segmentFraction);
                
                // 创建插值点，使用统一的飞行高度
                position = Cesium.Cartesian3.fromRadians(
                    lerpLon, 
                    lerpLat, 
                    flyHeight
                );
            }
            
            // 计算时间
            const timeSeconds = fraction * totalSeconds;
            const time = Cesium.JulianDate.addSeconds(
                actualStartTime,
                timeSeconds,
                new Cesium.JulianDate()
            );
            
            result.push({
                time: time,
                position: position
            });
        }
        
        return result;
    }
    
    /**
     * 更新实时信息
     */
    _updateRealTimeInfo(percentage) {
        if (!this.flyEntity || !this.isFlying) return;
        
        try {
            // 计算当前时间（秒）
            const totalDuration = Cesium.JulianDate.secondsDifference(this.stopTime, this.startTime);
            const elapsedTime = totalDuration * percentage;
            
            // 计算当前距离
            const totalLength = this.calculateLength();
            const currentLength = totalLength * percentage;
            
            // 获取当前位置
            const currentTime = this.viewer.clock.currentTime;
            const position = this.flyEntity.position.getValue(currentTime);
            
            if (!position) return;
            
            // 计算经纬度和高度
            const cartographic = Cesium.Cartographic.fromCartesian(position);
            const longitude = Cesium.Math.toDegrees(cartographic.longitude);
            const latitude = Cesium.Math.toDegrees(cartographic.latitude);
            const height = cartographic.height;
            
            // 触发更新事件
            const event = new CustomEvent('roamFlyTimeUpdate', {
                detail: {
                    time: elapsedTime,
                    len: currentLength,
                    point: {
                        x: longitude,
                        y: latitude,
                        z: height
                    }
                }
            });
            document.dispatchEvent(event);
        } catch (error) {
            console.error('更新实时信息错误:', error);
        }
    }
    
    /**
     * 停止漫游
     */
    stop() {
        // 停止自定义动画
        if (this._animationFrameId) {
            console.log('取消动画帧:', this._animationFrameId);
            cancelAnimationFrame(this._animationFrameId);
            this._animationFrameId = null;
        }
        
        // 清理动画数据
        this._animationData = null;
        this._boundAnimationTick = null;
        this._lastPathUpdatePercentage = 0;
        
        // 移除分段路径显示
        if (this._flownPathEntity) {
            this.viewer.entities.remove(this._flownPathEntity);
            this._flownPathEntity = null;
        }
        if (this._remainingPathEntity) {
            this.viewer.entities.remove(this._remainingPathEntity);
            this._remainingPathEntity = null;
        }
        
        // 恢复原始路径显示
        if (this.polyline && this.polyline.polyline) {
            this.polyline.polyline.show = true;
        }
        
        // 停止定时器
        if (this.timetik) {
            clearInterval(this.timetik);
            this.timetik = null;
        }
        
        // 重置视角和实体
        if (this.viewer) {
            this.viewer.trackedEntity = undefined;
            
            if (this.flyEntity) {
                this.viewer.entities.remove(this.flyEntity);
                this.flyEntity = null;
            }
            
            // 强制渲染一次
            this.viewer.scene.requestRender();
        }
        
        // 隐藏时间轴
        this._hideTimeline();
        
        // 重置状态
        this.isFlying = false;
        
        console.log('漫游已停止');
    }

    /**
     * 销毁实例
     */
    destroy() {
        this.stop();
        
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }

        if (this.polyline) {
            this.viewer.entities.remove(this.polyline);
            this.polyline = null;
        }
        
        // 移除时间轴
        if (this.timelineContainer) {
            document.body.removeChild(this.timelineContainer);
            this.timelineContainer = null;
        }

        this.positions = [];
        this.tempPositions = [];
    }

    /**
     * 更新样式
     * @param {Object} params 样式参数对象
     */
    updateStyle(params) {
        if (!params) return;
        
        let needReinitialize = false;
        let needUpdateView = false;
        
        // 速度更新
        if (params.hasOwnProperty('speed') && typeof params.speed === 'number') {
            this.speed = params.speed;
            console.log('更新飞行速度:', this.speed, 'km/h');
        }
        
        // 视角模式更新
        if (params.hasOwnProperty('viewMode')) {
            const newViewMode = Number(params.viewMode);
            if (this.options.viewMode !== newViewMode) {
                this.options.viewMode = newViewMode;
                console.log('更新视角模式:', this.options.viewMode);
                needUpdateView = true;
            }
        }
        
        // 高度系数更新
        if (params.hasOwnProperty('heightFactor') && typeof params.heightFactor === 'number') {
            this.options.heightFactor = params.heightFactor;
            console.log('更新高度系数:', this.options.heightFactor);
            needReinitialize = true;
        }
        
        // 视角距离更新
        if (params.hasOwnProperty('cameraDistance') && typeof params.cameraDistance === 'number') {
            this.options.cameraDistance = params.cameraDistance;
            console.log('更新视角距离:', this.options.cameraDistance);
            needUpdateView = true;
        }
        
        // 如果视角模式或视角距离变化，且正在飞行，应用新的视角模式
        if (needUpdateView && this.isFlying) {
            this._applyViewMode();
        }
        
        // 如果高度系数变化且已有路线，重新初始化路线以应用新高度
        if (needReinitialize && this.positions && this.positions.length > 2) {
            console.log('重新初始化路线以应用新的高度系数...');
            
            // 暂存原始坐标点（不带高度）
            const originalPositions = this.positions.map(pos => {
                const cart = Cesium.Cartographic.fromCartesian(pos);
                return Cesium.Cartesian3.fromRadians(
                    cart.longitude,
                    cart.latitude,
                    0 // 不保留高度，让initialize方法重新计算
                );
            });
            
            // 使用原始经纬度重新初始化，但应用新的高度系数
            this.initialize(originalPositions);
        }
    }

    /**
     * 暂停飞行
     * @returns {Boolean} 是否成功暂停
     */
    pause() {
        if (!this.isFlying || !this._animationData) {
            console.warn("没有正在进行的飞行，无法暂停");
            return false;
        }
        
        if (this._animationData.isPaused) {
            console.warn("飞行已经处于暂停状态");
            return false;
        }
        
        // 设置暂停状态
        this._animationData.isPaused = true;
        this._animationData.pauseStart = Date.now();
        
        console.log("飞行已暂停");
        
        // 查找播放/暂停按钮
        let playPauseBtn = null;
        if (this.timelineContainer) {
            playPauseBtn = this.timelineContainer.querySelector('#playPauseBtn');
        }
        if (!playPauseBtn) {
            playPauseBtn = document.getElementById('playPauseBtn');
        }
        
        // 更新时间轴按钮状态（如果有）
        if (playPauseBtn) {
            playPauseBtn.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                    <path d="M8 5v14l11-7z"/>
                </svg>
            `;
        }
        
        return true;
    }

    /**
     * 继续飞行
     * @returns {Boolean} 是否成功继续
     */
    continue() {
        if (!this.isFlying || !this._animationData) {
            console.warn("没有正在进行的飞行，无法继续");
            return false;
        }
        
        if (!this._animationData.isPaused) {
            console.warn("飞行已经处于进行状态");
            return false;
        }
        
        // 计算暂停的时间并添加到总暂停时间
        const now = Date.now();
        this._animationData.pausedTime += (now - this._animationData.pauseStart);
        
        // 取消暂停状态
        this._animationData.isPaused = false;
        
        console.log("飞行已继续");
        
        // 查找播放/暂停按钮
        let playPauseBtn = null;
        if (this.timelineContainer) {
            playPauseBtn = this.timelineContainer.querySelector('#playPauseBtn');
        }
        if (!playPauseBtn) {
            playPauseBtn = document.getElementById('playPauseBtn');
        }
        
        // 更新时间轴按钮状态（如果有）
        if (playPauseBtn) {
            playPauseBtn.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                </svg>
            `;
        }
        
        return true;
    }

    /**
     * 更新高度系数
     * @param {Number} heightFactor 高度系数
     */
    updateHeightFactor(heightFactor) {
        if (typeof heightFactor === 'number' && heightFactor >= 0) {
            this.updateStyle({ heightFactor });
        }
    }

    /**
     * 更新视角距离
     * @param {Number} cameraDistance 视角距离（米）
     */
    updateCameraDistance(cameraDistance) {
        if (typeof cameraDistance === 'number' && cameraDistance > 0) {
            this.updateStyle({ cameraDistance });
        }
    }
} 