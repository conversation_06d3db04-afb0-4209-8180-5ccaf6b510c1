/**
 * 测量工具重定向文件
 * 该文件用于兼容旧的引用路径，并确保MeasureTool类正确加载
 */

// 确保先加载MeasureHandler.js中的MeasureTool类
if (typeof MeasureTool === 'undefined') {
    // 动态加载MeasureHandler.js
    const script = document.createElement('script');
    script.src = 'src/features/按钮/测量工具/core/MeasureHandler.js';
    script.async = false; // 确保同步加载
    document.head.appendChild(script);
}

// 兼容旧的函数名
function initMeasureTool(viewer, containerId, options, toggleBtnId) {
    console.log('initMeasureTool被调用，重定向到MeasureTool');
    
    // 等待MeasureTool类加载完成
    return new Promise((resolve, reject) => {
        const checkMeasureTool = () => {
            if (typeof MeasureTool !== 'undefined') {
                try {
                    const tool = new MeasureTool({
                        viewer: viewer,
                        target: containerId,
                        terrainProvider: viewer.scene.terrainProvider,
                        show: true,
                        useInternalUI: false
                    });
                    resolve(tool);
                } catch (error) {
                    reject(error);
                }
            } else {
                setTimeout(checkMeasureTool, 50); // 50ms后重试
            }
        };
        checkMeasureTool();
    });
} 