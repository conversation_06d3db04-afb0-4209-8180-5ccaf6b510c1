/**
 * 地形开挖UI组件
 * 提供地形开挖功能的界面和事件处理
 */
class TerrainDigUI {
    /**
     * 创建地形开挖UI组件
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.handler = null;
        this.panelId = 'terrainDigPanel';
        this.toggleBtnId = 'toggleTerrainDig';
    }

    /**
     * 生成地形开挖组件的HTML代码
     * @returns {string} HTML代码字符串
     */
    generateHTML() {
        return `
        <!-- 地形开挖按钮 -->
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/地形开挖/assets/svg/terrain-dig.svg" alt="地形开挖">
            <div class="tooltip">地形开挖</div>
        </button>
        
        <!-- 地形开挖面板 -->
        <div class="terrain-dig-panel" id="${this.panelId}" style="display:none;">
            <div class="toolbar-panel-title">地形开挖</div>
            <div class="input-group">
                <label>开挖深度：<span id="depthValue">30米</span></label>
                <input type="range" id="digDepth" min="0" max="1000" value="30">
            </div>
            <div class="input-group">
                <label>开挖体积：<span id="volumeValue">0立方米</span></label>
            </div>
            <div class="input-group">
                <label>纹理设置</label>
                <div class="texture-group">
                    <div class="texture-item">
                        <label>底部纹理</label>
                        <select id="bottomTexture">
                            <option value="src/images/ter_analysis/excavationregion_side.jpg">纹理1</option>
                            <option value="src/images/ter_analysis/excavationregion_top.jpg">纹理2</option>
                        </select>
                    </div>
                    <div class="texture-item">
                        <label>侧面纹理</label>
                        <select id="sideTexture">
                            <option value="src/images/ter_analysis/excavationregion_top.jpg">纹理1</option>
                            <option value="src/images/ter_analysis/excavationregion_side.jpg">纹理2</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="button-group">
                <button class="start-btn" id="startDig">
                    <i class="fas fa-draw-polygon"></i>
                    开始绘制
                </button>
                <button class="clear-btn" id="clearDig">
                    <i class="fas fa-eraser"></i>
                    清除
                </button>
                <button class="exit-btn" id="exitDig">
                    <i class="fas fa-times"></i>
                    退出
                </button>
            </div>
        </div>`;
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/地形开挖/assets/svg/terrain-dig.svg" alt="地形开挖">
            <div class="tooltip">地形开挖</div>
        </button>`;
        
        container.appendChild(tempDiv.firstElementChild);
        console.log(`地形开挖按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 地形开挖面板 -->
        <div class="terrain-dig-panel" id="${this.panelId}" style="display:none;">
            <div class="toolbar-panel-title">地形开挖</div>
            <div class="input-group">
                <label>开挖深度：<span id="depthValue">30米</span></label>
                <input type="range" id="digDepth" min="0" max="1000" value="30">
            </div>
            <div class="input-group">
                <label>开挖体积：<span id="volumeValue">0立方米</span></label>
            </div>
            <div class="input-group">
                <label>纹理设置</label>
                <div class="texture-group">
                    <div class="texture-item">
                        <label>底部纹理</label>
                        <select id="bottomTexture">
                            <option value="src/images/ter_analysis/excavationregion_side.jpg">纹理1</option>
                            <option value="src/images/ter_analysis/excavationregion_top.jpg">纹理2</option>
                        </select>
                    </div>
                    <div class="texture-item">
                        <label>侧面纹理</label>
                        <select id="sideTexture">
                            <option value="src/images/ter_analysis/excavationregion_top.jpg">纹理1</option>
                            <option value="src/images/ter_analysis/excavationregion_side.jpg">纹理2</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="button-group">
                <button class="start-btn" id="startDig">
                    <i class="fas fa-draw-polygon"></i>
                    开始绘制
                </button>
                <button class="clear-btn" id="clearDig">
                    <i class="fas fa-eraser"></i>
                    清除
                </button>
                <button class="exit-btn" id="exitDig">
                    <i class="fas fa-times"></i>
                    退出
                </button>
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        console.log('地形开挖面板已添加到页面');
    }

    /**
     * 初始化地形开挖组件及事件绑定
     */
    init() {
        console.log('正在初始化地形开挖UI组件...');
        
        // 获取或创建地形开挖处理器实例
        this.handler = window.terrainDigHandler || TerrainDigHandler.init(this.viewer);
        window.terrainDigHandler = this.handler;
        
        // 切换面板显示/隐藏的全局函数
        window.toggleTerrainDigPanel = () => {
            console.log('直接调用toggleTerrainDigPanel函数');
            const panel = document.getElementById(this.panelId);
            
            if (panel) {
                const computedDisplay = window.getComputedStyle(panel).display;
                const isVisible = computedDisplay !== 'none';
                
                if (!isVisible) {
                    // 隐藏其他面板
                    ['searchPanel', 'measureToolContainer', 'profileAnalysisPanel'].forEach(id => {
                        const otherPanel = document.getElementById(id);
                        if (otherPanel) {
                            otherPanel.style.display = 'none';
                        }
                    });
                    
                    // 显示地形开挖面板
                    panel.style.display = 'block';
                    
                    // 设置面板位置
                    const toggleBtn = document.getElementById(this.toggleBtnId);
                    if (toggleBtn && typeof PanelPositioner !== 'undefined') {
                        try {
                            PanelPositioner.setPosition(toggleBtn, panel, {
                                preferredPosition: 'right',
                                gap: 10
                            });
                        } catch (e) {
                            console.error('面板定位出错:', e);
                            panel.style.position = 'fixed';
                            panel.style.top = '100px';
                            panel.style.left = '100px';
                        }
                    }
                } else {
                    // 隐藏面板
                    panel.style.display = 'none';
                    
                    // 如果正在绘制，清除绘制状态
                    if (this.handler && this.handler.isDrawing) {
                        this.handler.clearAll();
                    }
                }
            }
        };
        
        // 绑定按钮事件
        this.bindEvents();
        
        console.log('地形开挖UI组件初始化完成');
    }

    /**
     * 绑定事件处理函数
     */
    bindEvents() {
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            // 移除现有事件监听器
            const newBtn = toggleBtn.cloneNode(true);
            toggleBtn.parentNode.replaceChild(newBtn, toggleBtn);
            
            // 绑定新的事件监听器
            newBtn.addEventListener('click', () => {
                window.toggleTerrainDigPanel();
            });
        }
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {TerrainDigUI} 地形开挖UI实例
     */
    static init(viewer, toolButtonsId = 'toolButtons') {
        console.log('TerrainDigUI.init 被调用');
        const terrainDigUI = new TerrainDigUI(viewer);
        
        // 检查是否需要添加HTML元素
        if (!document.getElementById(terrainDigUI.toggleBtnId)) {
            terrainDigUI.appendButtonTo(toolButtonsId);
        }
        
        if (!document.getElementById(terrainDigUI.panelId)) {
            terrainDigUI.appendPanelToBody();
        }
        
        // 初始化地形开挖组件
        terrainDigUI.init();
        
        return terrainDigUI;
    }
}

// 导出到全局作用域
window.TerrainDigUI = TerrainDigUI; 