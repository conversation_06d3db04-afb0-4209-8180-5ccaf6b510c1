/* 打印功能样式 */

/* 打印面板 */
.print-panel {
    width: 300px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* 定制滚动条 */
.print-panel ::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

.print-panel ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.print-panel ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

/* 面板容器 */
.print-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 面板头部 */
.print-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background: #2b8df7;
    color: white;
    border-radius: 4px 4px 0 0;
}

.print-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
}

.print-header .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

/* 面板内容 */
.print-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 12px;
    max-height: calc(100vh - 120px);
}

/* 控制区域样式 */
.control-section {
    margin-bottom: 10px;
    padding-bottom: 8px;
}

/* 控制项 */
.control-item {
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
}

.control-item label {
    margin-bottom: 4px;
    font-size: 12px;
    color: #333;
}

.control-item input[type="text"] {
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    padding: 4px 6px;
    font-size: 12px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    transition: border-color 0.2s ease;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #333;
}

/* 按钮样式 */
.button-group {
    display: flex;
    gap: 6px;
    margin-top: 10px;
}

.btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.btn-primary {
    background-color: #2b8df7;
    color: white;
}

.btn-primary:hover {
    background-color: #2180e3;
}

/* 打印时不显示 */
@media print {
    .print-panel {
        display: none !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    body {
        margin: 0;
        padding: 0;
    }
    
    #cesiumContainer {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        page-break-inside: avoid;
    }
}

/* 黑暗主题适配 */
.dark .print-panel {
    background-color: rgba(40, 44, 52, 0.95);
    color: #e0e0e0;
}

.dark .control-item label {
    color: #e0e0e0;
}

.dark .control-item input[type="text"] {
    background-color: rgba(60, 64, 72, 0.95);
    color: #e0e0e0;
    border-color: #444;
} 