/* 书签管理相关样式 */

.bookmark-add-form {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bookmark-list {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 10px;
}

.bookmark-item {
    display: flex;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
}

.bookmark-item:hover {
    background-color: rgba(235, 245, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.bookmark-preview {
    width: 60px;
    height: 60px;
    margin-right: 10px;
    flex-shrink: 0;
}

.bookmark-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.bookmark-thumbnail-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 10px;
    color: #666;
    text-align: center;
}

.bookmark-info {
    flex: 1;
    overflow: hidden;
    padding: 5px 0;
}

.bookmark-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-date {
    font-size: 12px;
    color: #888;
}

.bookmark-actions {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
    padding: 5px 0;
}

.bookmark-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #555;
    transition: color 0.2s ease;
}

.bookmark-actions button:hover {
    color: #0077ff;
}

.btn-fly {
    margin-bottom: 5px;
}

.btn-delete:hover {
    color: #ff3333 !important;
}

.empty-message {
    text-align: center;
    padding: 20px 0;
    color: #888;
    font-style: italic;
} /* 
 * 书签管理CSS重定向文件
 * 该文件用于兼容旧的引用路径
 */

@import url('bookmark-manager.css'); 