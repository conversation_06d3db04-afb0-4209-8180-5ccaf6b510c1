/**
 * RoamFly UI控制器
 */
import { RoamFlyConfig } from '../config.js';
import { RoamFlyTool } from '../core/RoamFlyTool.js';

export class RoamFlyUI {
    /**
     * 初始化UI控制器
     * @param {Object} viewer Cesium viewer实例
     * @param {String} container 工具按钮容器ID
     */
    static init(viewer, container) {
        if (!RoamFlyUI.instance) {
            RoamFlyUI.instance = new RoamFlyUI(viewer, container);
        }
        return RoamFlyUI.instance;
    }

    constructor(viewer, container) {
        this.viewer = viewer;
        this.container = container;
        this.tool = null;
        this.panel = null;
        this.isActive = false;
        this.isDrawing = false;
        this.hasRoute = false;

        this._createToolButton();
        this._initEventListeners();
        this._addSvgIcon();
    }

    /**
     * 添加SVG图标到文档
     */
    _addSvgIcon() {
        // 检查是否已存在SVG容器
        let svgContainer = document.getElementById('tool-icons-container');
        if (!svgContainer) {
            svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svgContainer.id = 'tool-icons-container';
            svgContainer.style.display = 'none';
            document.body.appendChild(svgContainer);
        }

        // 添加图标定义
        const iconDef = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
        iconDef.innerHTML = RoamFlyConfig.ui.svgIcon;
        svgContainer.appendChild(iconDef);
    }

    /**
     * 创建工具按钮
     */
    _createToolButton() {
        const button = document.createElement('button');
        button.id = RoamFlyConfig.ui.buttonId;
        
        // 使用与其他按钮相同的HTML结构
        button.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24">
                <use xlink:href="#${RoamFlyConfig.ui.buttonIcon}"></use>
            </svg>
            <span class="tooltip">${RoamFlyConfig.ui.buttonTitle}</span>
        `;
        
        // 设置按钮顺序
        if (RoamFlyConfig.ui.buttonOrder) {
            button.style.order = RoamFlyConfig.ui.buttonOrder;
        }
        
        // 将按钮添加到工具容器
        document.getElementById(this.container).appendChild(button);
        button.addEventListener('click', () => this.toggleTool());
        
        console.log(`路线漫游按钮已添加到容器: ${this.container}`);
    }

    /**
     * 初始化事件监听
     */
    _initEventListeners() {
        // 监听实时信息更新
        document.addEventListener('roamFlyTimeUpdate', (e) => {
            this.updateRealTimeInfo(e.detail);
        });

        // 监听路线创建完成事件
        document.addEventListener('roamFlyRouteCreated', (e) => {
            this.onRouteCreated(e.detail);
        });

        // 监听路线初始化完成事件
        document.addEventListener('roamFlyRouteInitialized', (e) => {
            this.onRouteInitialized(e.detail);
        });
    }

    /**
     * 切换工具状态
     */
    toggleTool() {
        if (this.isActive) {
            this.deactivate();
        } else {
            this.activate();
        }
    }

    /**
     * 激活工具
     */
    activate() {
        if (this.isActive) return;

        this.isActive = true;
        this._createPanel();
        this.tool = new RoamFlyTool(this.viewer);
        
        // 更新按钮状态为活动状态
        const button = document.getElementById(RoamFlyConfig.ui.buttonId);
        if (button) {
            button.classList.add('active');
            console.log('激活路线漫游工具');
        }
    }

    /**
     * 停用工具
     */
    deactivate() {
        if (!this.isActive) return;

        this.isActive = false;
        
        if (this.tool) {
            this.tool.destroy();
            this.tool = null;
        }

        if (this.panel) {
            // 隐藏面板而不是移除
            this.panel.style.display = 'none';
        }

        // 更新按钮状态为非活动状态
        const button = document.getElementById(RoamFlyConfig.ui.buttonId);
        if (button) {
            button.classList.remove('active');
            console.log('停用路线漫游工具');
        }
    }

    /**
     * 创建控制面板
     */
    _createPanel() {
        const self = this;
        if (this.panel) {
            // 如果面板已存在，直接显示
            if (this.panel.style.display === 'none') {
                this.panel.style.display = 'block';
            }
            return;
        }

        // 确定面板位置
        let panelPosition = { right: '80px', top: '80px' };
        
        try {
            // 尝试检查是否有PanelPositioner工具
            if (typeof PanelPositioner !== 'undefined' && typeof PanelPositioner.getPosition === 'function') {
                const pos = PanelPositioner.getPosition('roamfly');
                if (pos) {
                    panelPosition = pos;
                }
            }
        } catch (e) {
            console.warn('使用默认面板位置', e);
        }

        // 创建面板DOM元素
        this.panel = document.createElement('div');
        this.panel.className = 'roamfly-panel';
        this.panel.style.position = 'absolute';
        this.panel.style.right = panelPosition.right || '80px';
        this.panel.style.top = panelPosition.top || '80px';
        this.panel.style.zIndex = '1000';
        
        // 创建面板HTML
        this.panel.innerHTML = `
            <div class="roamfly-container">
                <div class="roamfly-header">
                    <h3>路线漫游</h3>
                    <button class="close-button">&times;</button>
                </div>
                <div class="roamfly-content">
                    <!-- 路线设置区 -->
                    <div class="control-section">
                        <h4>路线设置</h4>
                        <div class="control-item">
                            <button id="drawRoute" class="btn btn-primary" style="width: 100%;">绘制路线</button>
                        </div>
                    </div>
                    
                    <!-- 基本信息区 -->
                    <div class="info-section">
                        <h4>基本信息</h4>
                        <div class="control-item">
                            <label>路线名称:</label>
                            <input type="text" id="routeName" class="form-control" placeholder="未命名路线">
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <div class="control-item" style="flex: 1;">
                                <label>视角模式:</label>
                                <select id="viewMode" class="form-control">
                                    <option value="1">跟随视角</option>
                                    <option value="2">锁定视角</option>
                                    <option value="3">自由视角</option>
                                </select>
                            </div>
                            <div class="info-item" style="flex: 1;">
                                <label>距离:</label>
                                <div class="value-display" id="distanceValue">0.00公里</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 视角设置区 -->
                    <div class="control-section">
                        <h4>视角设置</h4>
                        <div class="control-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <label>高度系数:</label>
                                <span id="heightFactorValue" style="font-size: 12px;">1.5</span>
                            </div>
                            <input type="range" id="heightFactor" class="form-control" min="0" max="50" step="0.5" value="1.5">
                        </div>
                        <div class="control-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <label>视角距离:</label>
                                <span id="cameraDistanceValue" style="font-size: 12px;">0.2千米</span>
                            </div>
                            <input type="range" id="cameraDistance" class="form-control" min="0.01" max="60" step="0.1" value="0.2">
                        </div>
                    </div>
                    
                    <!-- 漫游控制区 -->
                    <div class="control-section">
                        <h4>漫游控制</h4>
                        <div class="control-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <label>飞行速度:</label>
                                <span id="flySpeedValue" style="font-size: 12px;">100 km/h</span>
                            </div>
                            <input type="range" id="flySpeed" class="form-control" min="10" max="10000" step="100" value="100">
                        </div>
                        <div class="button-group">
                            <button id="startFly" class="btn btn-primary" disabled>开始漫游</button>
                            <button id="pauseFly" class="btn btn-primary" disabled>暂停</button>
                            <button id="stopFly" class="btn btn-danger" disabled>停止</button>
                        </div>
                    </div>
                    
                    <!-- 实时信息区 -->
                    <div class="info-section">
                        <h4>实时信息</h4>
                        <div class="grid-container">
                            <div class="info-item">
                                <label>总长度:</label>
                                <div class="value-display" id="totalLength">0.00公里</div>
                            </div>
                            <div class="info-item">
                                <label>已行驶:</label>
                                <div class="value-display" id="traveledLength">0.00公里</div>
                            </div>
                            <div class="info-item">
                                <label>总时间:</label>
                                <div class="value-display" id="totalTime">00:00:00</div>
                            </div>
                            <div class="info-item">
                                <label>已用时:</label>
                                <div class="value-display" id="usedTime">00:00:00</div>
                            </div>
                            <div class="info-item">
                                <label>当前位置:</label>
                                <div class="value-display" id="currentPos">E:0.000, N:0.000</div>
                            </div>
                            <div class="info-item">
                                <label>当前高程:</label>
                                <div class="value-display" id="currentHeight">0.00米</div>
                            </div>
                        </div>
                        <div class="info-item" style="margin-top: 6px;">
                            <label>完成进度:</label>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;

        // 将面板添加到DOM中
        document.body.appendChild(this.panel);

        // 绑定事件
        this._initPanelEvents();
    }

    /**
     * 初始化面板事件
     */
    _initPanelEvents() {
        // 初始化速度滑块范围
        const flySpeedInput = this.panel.querySelector('#flySpeed');
        if (flySpeedInput) {
            // 设置配置中的最小/最大速度值
            flySpeedInput.min = RoamFlyConfig.DEFAULT_SETTINGS.minSpeed;
            flySpeedInput.max = RoamFlyConfig.DEFAULT_SETTINGS.maxSpeed;
            flySpeedInput.value = RoamFlyConfig.DEFAULT_SETTINGS.speed;
            
            // 更新初始显示值
            const flySpeedDisplay = this.panel.querySelector('#flySpeedValue');
            if (flySpeedDisplay) {
                flySpeedDisplay.textContent = flySpeedInput.value + ' km/h';
            }
            
            // 添加事件监听器
            flySpeedInput.addEventListener('input', (e) => {
                if (this.tool) {
                    const value = parseInt(e.target.value);
                    this.tool.updateStyle({ speed: value });
                    if (flySpeedDisplay) {
                        flySpeedDisplay.textContent = value + ' km/h';
                    }
                }
            });
        }
        
        // 关闭按钮
        const closeButton = this.panel.querySelector('.close-button');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.deactivate());
        }

        // 绘制路线按钮
        const drawButton = this.panel.querySelector('#drawRoute');
        if (drawButton) {
            drawButton.addEventListener('click', () => {
                if (!this.isDrawing) {
                    this.isDrawing = true;
                    drawButton.textContent = '完成绘制';
                    drawButton.classList.add('active');
                    this.tool.startDrawing();
                } else {
                    this.isDrawing = false;
                    drawButton.textContent = '绘制路线';
                    drawButton.classList.remove('active');
                    this.tool.stopDrawing();
                }
            });
        }

        // 视角模式切换
        const viewModeSelect = this.panel.querySelector('#viewMode');
        if (viewModeSelect) {
            viewModeSelect.addEventListener('change', (e) => {
                if (this.tool) {
                    this.tool.updateStyle({ viewMode: parseInt(e.target.value) });
                }
            });
        }

        // 高度系数
        const heightFactorInput = this.panel.querySelector('#heightFactor');
        if (heightFactorInput) {
            const heightFactorDisplay = this.panel.querySelector('#heightFactorValue');
            heightFactorInput.addEventListener('input', (e) => {
                if (this.tool) {
                    const value = parseFloat(e.target.value);
                    this.tool.updateStyle({ heightFactor: value });
                    if (heightFactorDisplay) {
                        heightFactorDisplay.textContent = value.toFixed(1);
                    }
                }
            });
        }

        // 视角距离
        const cameraDistanceInput = this.panel.querySelector('#cameraDistance');
        if (cameraDistanceInput) {
            const cameraDistanceDisplay = this.panel.querySelector('#cameraDistanceValue');
            cameraDistanceInput.addEventListener('input', (e) => {
                if (this.tool) {
                    const value = parseFloat(e.target.value);
                    // 转换为米，因为RoamFlyTool内部使用米为单位
                    const valueInMeters = this._kilometersToMeters(value);
                    this.tool.updateStyle({ cameraDistance: valueInMeters });
                    if (cameraDistanceDisplay) {
                        // 确保总是显示一位小数的千米单位
                        cameraDistanceDisplay.textContent = this._formatKilometers(value) + '千米';
                    }
                }
            });
        }

        // 开始按钮
        const startButton = this.panel.querySelector('#startFly');
        if (startButton) {
            startButton.addEventListener('click', () => {
                if (this.tool) {
                    if (!this.hasRoute) {
                        alert('请先绘制漫游路线！');
                        return;
                    }
                    
                    // 禁用开始按钮，启用暂停和停止按钮
                    startButton.disabled = true;
                    this.panel.querySelector('#pauseFly').disabled = false;
                    this.panel.querySelector('#stopFly').disabled = false;
                    
                    // 启动漫游
                    const success = this.tool.start();
                    
                    if (success) {
                        console.log('漫游已开始');
                    } else {
                        // 如果启动失败，恢复按钮状态
                        startButton.disabled = false;
                        this.panel.querySelector('#pauseFly').disabled = true;
                        this.panel.querySelector('#stopFly').disabled = true;
                        alert('漫游启动失败，请检查路线是否有效');
                    }
                }
            });
        }

        // 暂停按钮
        const pauseButton = this.panel.querySelector('#pauseFly');
        if (pauseButton) {
            pauseButton.addEventListener('click', () => {
                if (this.tool) {
                    if (pauseButton.textContent === '暂停') {
                        this.tool.pause();
                        pauseButton.textContent = '继续';
                        console.log('漫游已暂停');
                    } else {
                        this.tool.continue();
                        pauseButton.textContent = '暂停';
                        console.log('漫游已继续');
                    }
                }
            });
        }

        // 停止按钮
        const stopButton = this.panel.querySelector('#stopFly');
        if (stopButton) {
            stopButton.addEventListener('click', () => {
                if (this.tool) {
                    // 停止漫游
                    this.tool.stop();
                    
                    // 更新按钮状态
                    stopButton.disabled = true;
                    this.panel.querySelector('#pauseFly').disabled = true;
                    this.panel.querySelector('#pauseFly').textContent = '暂停';
                    this.panel.querySelector('#startFly').disabled = false;
                    
                    console.log('漫游已停止');
                }
            });
        }
    }

    /**
     * 路线创建完成回调
     */
    onRouteCreated(detail) {
        if (!this.panel) return;
        
        const drawButton = this.panel.querySelector('#drawRoute');
        if (drawButton) {
            drawButton.textContent = '绘制路线';
            drawButton.classList.remove('active');
        }
        this.isDrawing = false;
        this.hasRoute = true;

        // 启用开始按钮
        const startButton = this.panel.querySelector('#startFly');
        if (startButton) {
            startButton.disabled = false;
        }
    }

    /**
     * 路线初始化完成回调
     */
    onRouteInitialized(detail) {
        this.updatePanelInfo(detail);
        this.hasRoute = true;
        
        // 启用漫游控制按钮
        if (this.panel) {
            const startButton = this.panel.querySelector('#startFly');
            if (startButton) {
                startButton.disabled = false;
            }
        }
    }

    /**
     * 重置面板信息
     */
    _resetPanelInfo() {
        if (!this.panel) return;
        
        const routeNameInput = this.panel.querySelector('#routeName');
        const distanceValue = this.panel.querySelector('#distanceValue');
        const heightValue = this.panel.querySelector('#heightValue');
        const totalLength = this.panel.querySelector('#totalLength');
        const traveledLength = this.panel.querySelector('#traveledLength');
        const totalTime = this.panel.querySelector('#totalTime');
        const usedTime = this.panel.querySelector('#usedTime');
        const currentPos = this.panel.querySelector('#currentPos');
        const currentHeight = this.panel.querySelector('#currentHeight');
        const progressBar = this.panel.querySelector('#progressBar');
        const startButton = this.panel.querySelector('#startFly');
        const pauseButton = this.panel.querySelector('#pauseFly');
        const stopButton = this.panel.querySelector('#stopFly');

        if (routeNameInput && routeNameInput.tagName === 'INPUT') {
            routeNameInput.value = '';
        }
        if (distanceValue) distanceValue.textContent = '0.00';
        if (heightValue) heightValue.textContent = '0.00';
        if (totalLength) totalLength.textContent = '0.00公里';
        if (traveledLength) traveledLength.textContent = '0.00公里';
        if (totalTime) totalTime.textContent = '00:00:00';
        if (usedTime) usedTime.textContent = '00:00:00';
        if (currentPos) currentPos.textContent = 'E:0.000, N:0.000';
        if (currentHeight) currentHeight.textContent = '0.00米';
        if (progressBar) {
            progressBar.style.width = '0%';
        }
        if (startButton) startButton.disabled = true;
        if (pauseButton) pauseButton.disabled = true;
        if (stopButton) stopButton.disabled = true;
        this.hasRoute = false;
    }

    /**
     * 更新面板信息
     */
    updatePanelInfo(info) {
        if (!this.panel) return;
        
        const { name, alllen, alltime } = info;
        
        // 更新基本信息
        const nameElement = this.panel.querySelector('#routeName');
        if (nameElement && nameElement.tagName === 'INPUT') {
            nameElement.value = name || '未命名路线';
        }

        const distanceValue = this.panel.querySelector('#distanceValue');
        if (distanceValue) {
            const kilometers = (alllen / 1000).toFixed(2);
            distanceValue.textContent = kilometers + '公里';
        }

        const totalLength = this.panel.querySelector('#totalLength');
        if (totalLength) {
            const kilometers = (alllen / 1000).toFixed(2);
            totalLength.textContent = `${kilometers}公里`;
        }

        const totalTime = this.panel.querySelector('#totalTime');
        if (totalTime) {
            // 格式化为 时:分:秒
            const hours = Math.floor(alltime / 3600);
            const minutes = Math.floor((alltime % 3600) / 60);
            const seconds = Math.floor(alltime % 60);
            totalTime.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 更新实时信息
     */
    updateRealTimeInfo(info) {
        if (!this.panel) return;
        
        const {
            time, len, point: { x, y, z }
        } = info;

        // 更新实时信息
        const usedTime = this.panel.querySelector('#usedTime');
        if (usedTime) {
            // 格式化为 时:分:秒
            const hours = Math.floor(time / 3600);
            const minutes = Math.floor((time % 3600) / 60);
            const seconds = Math.floor(time % 60);
            usedTime.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        const traveledLength = this.panel.querySelector('#traveledLength');
        if (traveledLength) {
            const kilometers = (len / 1000).toFixed(2);
            traveledLength.textContent = `${kilometers}公里`;
        }

        const currentPos = this.panel.querySelector('#currentPos');
        if (currentPos) {
            currentPos.textContent = `E:${x.toFixed(6)}, N:${y.toFixed(6)}`;
        }

        const currentHeight = this.panel.querySelector('#currentHeight');
        if (currentHeight) {
            currentHeight.textContent = `${z.toFixed(2)}米`;
        }

        // 更新进度条
        const progressBar = this.panel.querySelector('#progressBar');
        if (progressBar && this.tool && this.tool.flyLine) {
            const progress = (len / this.tool.flyLine.alllen) * 100;
            progressBar.style.width = progress + '%';
        }
    }

    // 创建视角设置区域
    _createPerspectiveSettings() {
        const { DEFAULT_SETTINGS } = RoamFlyConfig;
        
        return `
            <div class="roam-perspective-settings">
                <div class="roam-section-title">视角设置</div>
                <div class="roam-input-group">
                    <label for="heightFactorInput">高度系数:</label>
                    <div class="input-wrapper">
                        <input type="number" id="heightFactorInput" value="${DEFAULT_SETTINGS.heightFactor}" min="${DEFAULT_SETTINGS.minHeightFactor}" max="${DEFAULT_SETTINGS.maxHeightFactor}" step="0.5" />
                        <input type="range" id="heightFactorRange" value="${DEFAULT_SETTINGS.heightFactor}" min="${DEFAULT_SETTINGS.minHeightFactor}" max="${DEFAULT_SETTINGS.maxHeightFactor}" step="0.5" />
                    </div>
                </div>
                <div class="roam-input-group">
                    <label for="cameraDistanceInput">视角距离:</label>
                    <div class="input-wrapper">
                        <input type="number" id="cameraDistanceInput" value="${(DEFAULT_SETTINGS.cameraDistance/1000).toFixed(1)}" min="${(DEFAULT_SETTINGS.minCameraDistance/1000).toFixed(1)}" max="${(DEFAULT_SETTINGS.maxCameraDistance/1000).toFixed(1)}" step="0.1" />
                        <span class="unit">千米</span>
                        <input type="range" id="cameraDistanceRange" value="${(DEFAULT_SETTINGS.cameraDistance/1000).toFixed(1)}" min="${(DEFAULT_SETTINGS.minCameraDistance/1000).toFixed(1)}" max="${(DEFAULT_SETTINGS.maxCameraDistance/1000).toFixed(1)}" step="0.1" />
                    </div>
                </div>
            </div>
        `;
    }

    // 添加视角设置事件
    _addPerspectiveSettingsEvents() {
        // 高度系数输入事件
        this._bindRangeInput('heightFactorInput', 'heightFactorRange', (value) => {
            this._settings.heightFactor = Number(value);
            this._roamFlyTool.updateHeightFactor(this._settings.heightFactor);
        });

        // 视角距离输入事件
        this._bindRangeInput('cameraDistanceInput', 'cameraDistanceRange', (value) => {
            // 将千米转换为米
            const distanceInMeters = this._kilometersToMeters(Number(value));
            this._settings.cameraDistance = distanceInMeters;
            this._roamFlyTool.updateCameraDistance(distanceInMeters);
            
            // 更新显示值，确保显示一位小数的千米单位
            const displayElement = document.querySelector('.unit').previousElementSibling;
            if (displayElement) {
                displayElement.value = this._formatKilometers(Number(value));
            }
        });
    }
    
    /**
     * 将千米转换为米
     * @param {Number} kilometers 千米值
     * @returns {Number} 米值
     */
    _kilometersToMeters(kilometers) {
        return kilometers * 1000;
    }
    
    /**
     * 格式化千米值，保留一位小数
     * @param {Number} kilometers 千米值
     * @returns {String} 格式化后的千米值
     */
    _formatKilometers(kilometers) {
        return kilometers.toFixed(1);
    }
} 