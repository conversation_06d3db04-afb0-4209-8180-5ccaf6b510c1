/**
 * 测量工具配置
 * @module MeasureConfig
 */

export const MeasureConfig = {
    // 模块基础信息
    id: 'measure',
    name: '测量工具',
    version: '1.0.0',
    
    // UI配置
    ui: {
        // 按钮配置
        button: {
            id: 'toggleMeasure',
            tooltip: '测量工具',
            iconPath: 'src/features/按钮/测量工具/assets/svg/measure.svg'
        },
        
        // 面板配置
        panel: {
            id: 'measurePanel',
            title: '测量工具',
            width: 280,
            height: 'auto'
        },
        
        // 样式文件
        styles: [
            'src/features/按钮/测量工具/styles/measure.css',
            'src/features/按钮/测量工具/styles/measureTool.css'
        ]
    },
    
    // 功能配置
    features: {
        // 测量类型
        types: {
            distance: { name: '距离测量', enabled: true },
            area: { name: '面积测量', enabled: true },
            height: { name: '高度测量', enabled: true },
            spaceDistance: { name: '空间距离', enabled: true },
            triangle: { name: '三角测量', enabled: true },
            squareAngle: { name: '角度测量', enabled: true }
        },
        
        // 测量精度
        precision: {
            distance: 2, // 小数点后位数
            area: 2,
            height: 2,
            angle: 1
        },
        
        // 单位设置
        units: {
            distance: 'm', // m, km
            area: 'm²',    // m², km²
            height: 'm',
            angle: '°'
        }
    },
    
    // 样式配置
    styles: {
        // 线条样式
        line: {
            color: '#ffff00',
            width: 2,
            clampToGround: true
        },
        
        // 标签样式
        label: {
            font: '14pt Arial',
            fillColor: '#ffffff',
            outlineColor: '#000000',
            outlineWidth: 2,
            style: 'FILL_AND_OUTLINE'
        },
        
        // 点样式
        point: {
            color: '#ffff00',
            pixelSize: 8,
            outlineColor: '#000000',
            outlineWidth: 2
        }
    }
};