/* 基础样式 */
body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: Arial, sans-serif;
    height: 100vh;
    width: 100vw;
}

#cesiumContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 隐藏Cesium默认控件 */
.cesium-viewer-bottom,
.cesium-toolbar-button {
    display: none !important;
}

/* SVG容器 */
#svg-container {
    display: none;
}

/* 通用面板基础样式 */
.analysis-panel {
    position: fixed;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(200, 200, 200, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
}

/* 通用输入框样式 */
.analysis-panel input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 5px 0px;
    border-radius: 4px;
    outline: none;
}

.analysis-panel input:focus {
    border-color: #00dcff;
    box-shadow: 0 0 5px rgba(0, 220, 255, 0.3);
}

/* 通用标题样式 */
.panel-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    color: #333;
}

/* 通用按钮组样式 */
.button-group {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

#measureToolContainer {
    position: fixed;
    bottom: 100px;
    left: 20px;
    z-index: 1000;
    width: auto;
    min-width: 150px;
}

#measureToolContainer .measureTool {
    width: 100%;
    box-sizing: border-box;
}

/* 图层切换面板特殊样式 */
#layerPanel {
    left: 20px;
    right: auto;
    top: 100px;
    transform: none;
    width: auto;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(200, 200, 200, 0.3);
    backdrop-filter: blur(10px);
    padding: 15px;
}

#layerPanel .button-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

#layerPanel button {
    width: 100%;
    text-align: left;
    padding: 10px 12px;
    margin: 0;
    white-space: nowrap;
    font-size: 13px;
    background-color: rgba(240, 240, 240, 0.9);
    color: #444;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

#layerPanel button:hover {
    background-color: rgba(230, 230, 230, 0.95);
    transform: translateX(3px);
}

/* 工具按钮样式 */
#toolButtons {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

#toolButtons button {
    width: 48px;
    height: 48px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    position: relative;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#toolButtons button img {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

#toolButtons button svg {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

#toolButtons button:hover {
    background-color: #2196F3;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 保持现代化彩色图标的原始颜色 */
#toolButtons button:hover img,
#toolButtons button:hover svg {
    transform: scale(1.1);
    /* 移除滤镜，保持彩色图标的原始颜色 */
}

#toolButtons .tooltip {
    position: absolute;
    right: 60px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    pointer-events: none;
}

#toolButtons button:hover .tooltip {
    opacity: 1;
    visibility: visible;
} 