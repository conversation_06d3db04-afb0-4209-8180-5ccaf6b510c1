/* 剖面分析按钮样式 */

.toolbar-button {
    width: 32px;
    height: 32px;
    background: rgba(38, 38, 38, 0.75);
    border: none;
    border-radius: 50%;
    margin: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.toolbar-button:hover {
    background: rgba(58, 58, 58, 0.85);
}

.toolbar-button svg {
    width: 20px;
    height: 20px;
    fill: #ffffff;
}

/* 剖面分析按钮特定样式 */
.profile-analysis-button svg {
    stroke: #ffffff;
    stroke-width: 2;
    fill: none;
}

/* ECharts 图表样式覆盖 */
#profileChart {
    background: transparent !important;
}

#profileChart .echarts-tooltip {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    padding: 8px !important;
    color: #333 !important;
}

/* 剖面分析面板样式 */
.profile-panel {
    position: absolute;
    left: 20px;
    top: 50px;
    width: 320px !important;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    z-index: 1000;
}

/* 面板标题 */
.toolbar-panel-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* 数据项布局 */
.submergence-data {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 10px;
}

.toolbar-data-item {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.toolbar-data-item label {
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
    display: block;
}

.toolbar-data-item .value {
    color: #1890ff;
    font-size: 12px;
    font-weight: 500;
}

/* 图表容器样式 */
#profileChart {
    width: 100% !important;
    height: 200px !important;
    margin: 10px 0;
    background: transparent !important;
    border-radius: 4px;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 6px;
}

.button-group button {
    width: 100%;
    padding: 6px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
    height: 28px;
    line-height: 1;
}

.button-group button img {
    width: 14px;
    height: 14px;
}

/* 按钮颜色样式 */
button.primary-btn {
    background: #1890ff !important;
    color: white !important;
}

button.primary-btn:hover {
    background: #40a9ff !important;
}

button.secondary-btn {
    background: #ff9800 !important;
    color: white !important;
}

button.secondary-btn:hover {
    background: #ffa726 !important;
}

button.danger-btn {
    background: #ff4d4f !important;
    color: white !important;
}

button.danger-btn:hover {
    background: #ff7875 !important;
}

/* 输入组样式 */
.input-group {
    margin-bottom: 6px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.input-group input {
    width: calc(100% - 16px);
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    font-size: 12px;
}

/* ECharts 图表样式覆盖 */
#profileChart .echarts-tooltip {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border: none !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    padding: 6px 8px !important;
    color: #333 !important;
    font-size: 12px !important;
}

/* 剖面分析按钮样式 */
.draw-profile-btn {
    background: #1890ff;
    color: white;
}

.draw-profile-btn:hover {
    background: #40a9ff;
}

.clear-profile-btn {
    background: #ff9800;
    color: white;
}

.clear-profile-btn:hover {
    background: #ffa726;
}

.exit-profile-btn {
    background: #ff4d4f;
    color: white;
}

/* 剖面分析点标注样式 */
.profile-point-label {
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #333;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    pointer-events: none;
    white-space: nowrap;
    z-index: 1000;
}

.profile-point {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.start-point {
    background: #1890ff;
}

.end-point {
    background: #ff4d4f;
} 