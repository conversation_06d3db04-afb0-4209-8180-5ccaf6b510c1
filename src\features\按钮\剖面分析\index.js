/**
 * 剖面分析模块入口文件
 * @module ProfileAnalysisModule
 */

// 导入核心模块
import { ProfileAnalysis } from './core/ProfileAnalysis.js';
import { ProfileAnalysisUI } from './ui/ProfileAnalysisUI.js';
import { ProfileAnalysisConfig } from './config.js';

/**
 * 剖面分析模块
 */
export class ProfileAnalysisModule {
    constructor(viewer) {
        this.viewer = viewer;
        this.config = ProfileAnalysisConfig;
        this.tool = null;
        this.ui = null;
    }

    /**
     * 初始化模块
     * @param {string} containerId - 容器ID
     * @returns {Promise<ProfileAnalysisModule>}
     */
    async init(containerId = 'toolButtons') {
        try {
            // 初始化UI
            this.ui = ProfileAnalysisUI.init(this.viewer, containerId);
            
            // 获取工具实例（UI初始化时已创建）
            this.tool = this.ui.profileAnalysis;
            
            return this;
        } catch (error) {
            console.error('剖面分析模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁模块
     */
    destroy() {
        if (this.ui) {
            this.ui.destroy && this.ui.destroy();
        }
        if (this.tool) {
            this.tool.destroy && this.tool.destroy();
        }
    }
}

// 向后兼容导出
export { ProfileAnalysis } from './core/ProfileAnalysis.js';
export { ProfileAnalysisUI } from './ui/ProfileAnalysisUI.js';
export { ProfileAnalysisConfig } from './config.js';

// 全局访问
window.ProfileAnalysisModule = ProfileAnalysisModule;