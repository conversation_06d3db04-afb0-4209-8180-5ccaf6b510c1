/**
 * 面板定位工具类
 * 用于计算和设置面板相对于按钮的最佳位置
 */
class PanelPositioner {
    /**
     * 计算面板的最佳位置
     * @param {HTMLElement} button - 触发按钮元素
     * @param {HTMLElement} panel - 面板元素
     * @param {Object} options - 配置选项
     * @param {number} options.gap - 面板与按钮之间的间距（像素）
     * @param {string} options.preferredPosition - 首选位置 ('right', 'left', 'top', 'bottom')
     * @returns {Object} 包含top和left属性的位置对象
     */
    static calculatePosition(button, panel, options = {}) {
        const {
            gap = 10,
            preferredPosition = 'right'
        } = options;

        // 获取按钮的位置和尺寸
        const buttonRect = button.getBoundingClientRect();
        // 获取面板的尺寸
        const panelRect = panel.getBoundingClientRect();
        // 获取视窗尺寸
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 初始化位置对象
        let position = {
            top: 0,
            left: 0
        };

        // 根据首选位置计算初始位置
        switch (preferredPosition) {
            case 'right':
                position = this.calculateRightPosition(buttonRect, panelRect, gap, viewportWidth);
                break;
            case 'left':
                position = this.calculateLeftPosition(buttonRect, panelRect, gap);
                break;
            case 'top':
                position = this.calculateTopPosition(buttonRect, panelRect, gap);
                break;
            case 'bottom':
                position = this.calculateBottomPosition(buttonRect, panelRect, gap, viewportHeight);
                break;
        }

        // 确保面板不会超出视窗边界
        position = this.adjustPositionToViewport(position, panelRect, viewportWidth, viewportHeight);

        return position;
    }

    /**
     * 计算面板在按钮右侧的位置
     */
    static calculateRightPosition(buttonRect, panelRect, gap, viewportWidth) {
        let left = buttonRect.right + gap;
        // 如果面板会超出右边界，尝试放置在左侧
        if (left + panelRect.width > viewportWidth) {
            left = buttonRect.left - gap - panelRect.width;
        }
        return {
            top: buttonRect.top,
            left: left
        };
    }

    /**
     * 计算面板在按钮左侧的位置
     */
    static calculateLeftPosition(buttonRect, panelRect, gap) {
        return {
            top: buttonRect.top,
            left: buttonRect.left - gap - panelRect.width
        };
    }

    /**
     * 计算面板在按钮上方的位置
     */
    static calculateTopPosition(buttonRect, panelRect, gap) {
        return {
            top: buttonRect.top - gap - panelRect.height,
            left: buttonRect.left
        };
    }

    /**
     * 计算面板在按钮下方的位置
     */
    static calculateBottomPosition(buttonRect, panelRect, gap, viewportHeight) {
        let top = buttonRect.bottom + gap;
        // 如果面板会超出底部边界，尝试放置在上方
        if (top + panelRect.height > viewportHeight) {
            top = buttonRect.top - gap - panelRect.height;
        }
        return {
            top: top,
            left: buttonRect.left
        };
    }

    /**
     * 调整位置确保面板在视窗内
     */
    static adjustPositionToViewport(position, panelRect, viewportWidth, viewportHeight) {
        const safeMargin = 15; // 安全边距
        const bottomSafeArea = 80; // 底部按钮区域安全高度
        
        // 确保不超出左边界
        if (position.left < safeMargin) {
            position.left = safeMargin;
        }
        // 确保不超出右边界
        if (position.left + panelRect.width > viewportWidth - safeMargin) {
            position.left = viewportWidth - panelRect.width - safeMargin;
        }
        // 确保不超出上边界
        if (position.top < safeMargin) {
            position.top = safeMargin;
        }
        // 确保不超出下边界并且不遮挡底部按钮区域
        if (position.top + panelRect.height > viewportHeight - bottomSafeArea) {
            position.top = Math.min(
                viewportHeight - panelRect.height - safeMargin,  // 防止面板超出屏幕
                viewportHeight - bottomSafeArea - panelRect.height  // 防止遮挡底部按钮
            );
            // 如果面板高度过高，优先确保顶部可见
            if (position.top < safeMargin) {
                position.top = safeMargin;
            }
        }

        return position;
    }

    /**
     * 应用位置到面板元素
     * @param {HTMLElement} panel - 面板元素
     * @param {Object} position - 位置对象
     */
    static applyPosition(panel, position) {
        panel.style.position = 'fixed';
        panel.style.top = `${position.top}px`;
        panel.style.left = `${position.left}px`;
    }

    /**
     * 设置面板位置
     * @param {HTMLElement} button - 触发按钮元素
     * @param {HTMLElement} panel - 面板元素
     * @param {Object} options - 配置选项
     */
    static setPosition(button, panel, options = {}) {
        const position = this.calculatePosition(button, panel, options);
        this.applyPosition(panel, position);
    }
}

// 将PanelPositioner添加到全局作用域
window.PanelPositioner = PanelPositioner; 