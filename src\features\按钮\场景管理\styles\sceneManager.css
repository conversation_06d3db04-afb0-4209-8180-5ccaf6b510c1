/* 场景管理器样式 */

/* 场景管理面板 */
.scene-manager-panel {
    width: 320px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    padding: 15px;
    display: none;
    position: absolute;
    z-index: 1000;
    max-height: 500px;
    overflow-y: auto;
}

/* 面板标题 */
.scene-manager-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 场景列表容器 */
.scene-list-container {
    margin-bottom: 15px;
    max-height: 350px;
    overflow-y: auto;
}

/* 场景列表项 */
.scene-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 8px;
    background-color: rgba(240, 240, 240, 0.5);
    transition: all 0.2s ease;
}

.scene-item:hover {
    background-color: rgba(230, 230, 230, 0.8);
}

.scene-item.active {
    background-color: rgba(33, 150, 243, 0.1);
    border-left: 3px solid #2196F3;
}

/* 场景名称 */
.scene-name {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    color: #333;
}

/* 场景项按钮 */
.scene-item-buttons {
    display: flex;
    gap: 5px;
}

.scene-item-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s ease;
}

.scene-item-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #2196F3;
}

.scene-load-button {
    color: #2196F3;
}

.scene-delete-button {
    color: #F44336;
}

.scene-rename-button {
    color: #4CAF50;
}

/* 底部按钮组 */
.scene-manager-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.btn-scene-manager {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-save-scene {
    background-color: #2196F3;
    color: white;
}

.btn-save-scene:hover {
    background-color: #1976D2;
}

.btn-close-panel {
    background-color: #e0e0e0;
    color: #333;
}

.btn-close-panel:hover {
    background-color: #d5d5d5;
}

/* 无场景提示 */
.no-scenes-message {
    padding: 20px;
    text-align: center;
    color: #999;
    font-style: italic;
}

/* 重命名输入框 */
.scene-rename-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 10px;
}

/* 保存场景对话框 */
.save-scene-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 20px;
    z-index: 1100;
    width: 320px;
    display: none;
}

.save-scene-dialog-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.save-scene-dialog input {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 15px;
}

.save-scene-dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-cancel {
    background-color: #e0e0e0;
    color: #333;
}

.btn-confirm {
    background-color: #2196F3;
    color: white;
}

/* 背景遮罩 */
.scene-manager-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: none;
} 