/**
 * 面板管理器 - 统一创建、定位和管理所有工具面板
 * 解决面板创建逻辑重复、定位不统一、模板化程度低的问题
 */
class PanelManager {
    constructor(eventBus = null) {
        this.eventBus = eventBus;
        this.panels = new Map(); // 面板实例映射
        this.templates = new Map(); // 面板模板映射
        this.activePanel = null; // 当前激活的面板
        
        // 面板计数器
        this.panelCounter = 0;
        
        // 默认配置
        this.defaultConfig = {
            width: 300,
            height: 'auto',
            position: 'right',
            gap: 10,
            modal: false,
            resizable: false,
            draggable: false,
            closeOnClickOutside: true,
            animation: true
        };

        this._initializeTemplates();
        this._setupGlobalEvents();
        
        console.log('✓ PanelManager 面板管理器初始化完成');
    }

    /**
     * 初始化内置面板模板
     */
    _initializeTemplates() {
        // 基础工具面板模板
        this.registerTemplate('default-panel', {
            header: true,
            footer: false,
            content: `
                <div class="panel-content">
                    <div class="panel-placeholder">
                        <p>默认面板内容</p>
                    </div>
                </div>
            `
        });

        // 测量工具面板模板
        this.registerTemplate('measurement-panel', {
            header: true,
            footer: true,
            content: `
                <div class="panel-content">
                    <div class="measure-buttons">
                        <button class="tool-btn" data-action="measure-distance">
                            <i class="icon-distance"></i>
                            <span>距离测量</span>
                        </button>
                        <button class="tool-btn" data-action="measure-area">
                            <i class="icon-area"></i>
                            <span>面积测量</span>
                        </button>
                        <button class="tool-btn" data-action="measure-height">
                            <i class="icon-height"></i>
                            <span>高度测量</span>
                        </button>
                        <button class="tool-btn" data-action="measure-angle">
                            <i class="icon-angle"></i>
                            <span>角度测量</span>
                        </button>
                    </div>
                </div>
            `,
            footer: `
                <div class="panel-actions">
                    <button class="btn-secondary" data-action="clear-all">清除全部</button>
                    <button class="btn-primary" data-action="close">关闭</button>
                </div>
            `
        });

        // 搜索面板模板
        this.registerTemplate('search-panel', {
            header: true,
            footer: false,
            content: `
                <div class="panel-content">
                    <div class="search-form">
                        <div class="form-group">
                            <label>坐标搜索</label>
                            <input type="text" placeholder="经度,纬度 或 度分秒格式" class="form-input" data-field="coordinates">
                        </div>
                        <div class="form-group">
                            <label>地名搜索</label>
                            <input type="text" placeholder="输入地名进行搜索" class="form-input" data-field="placename">
                        </div>
                        <div class="form-actions">
                            <button class="btn-primary" data-action="search">搜索</button>
                            <button class="btn-secondary" data-action="clear-markers">清除标记</button>
                        </div>
                    </div>
                </div>
            `
        });

        // 列表面板模板（用于书签、场景等）
        this.registerTemplate('list-panel', {
            header: true,
            footer: true,
            content: `
                <div class="panel-content">
                    <div class="list-container">
                        <div class="list-header">
                            <button class="btn-primary btn-sm" data-action="add-item">
                                <i class="icon-plus"></i> 添加
                            </button>
                        </div>
                        <div class="list-content" data-list="items">
                            <!-- 动态列表项将在这里生成 -->
                        </div>
                    </div>
                </div>
            `,
            footer: `
                <div class="panel-actions">
                    <button class="btn-secondary" data-action="export">导出</button>
                    <button class="btn-primary" data-action="close">关闭</button>
                </div>
            `
        });

        // 表单面板模板
        this.registerTemplate('form-panel', {
            header: true,
            footer: true,
            content: `
                <div class="panel-content">
                    <form class="panel-form" data-form="main">
                        <!-- 表单字段将动态添加 -->
                    </form>
                </div>
            `,
            footer: `
                <div class="panel-actions">
                    <button class="btn-secondary" data-action="reset">重置</button>
                    <button class="btn-primary" data-action="submit">确定</button>
                    <button class="btn-secondary" data-action="close">取消</button>
                </div>
            `
        });

        console.log('✓ 内置面板模板注册完成');
    }

    /**
     * 设置全局事件监听
     */
    _setupGlobalEvents() {
        // ESC键关闭面板
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.activePanel) {
                this.closePanel(this.activePanel);
            }
        });

        // 点击外部关闭面板
        document.addEventListener('click', (event) => {
            if (this.activePanel) {
                const panelData = this.panels.get(this.activePanel);
                if (panelData && panelData.config.closeOnClickOutside) {
                    const panelElement = panelData.element;
                    if (!panelElement.contains(event.target)) {
                        // 延时处理避免与按钮点击冲突
                        setTimeout(() => {
                            this.closePanel(this.activePanel);
                        }, 100);
                    }
                }
            }
        });
    }

    /**
     * 注册面板模板
     */
    registerTemplate(templateId, template) {
        this.templates.set(templateId, {
            header: template.header || false,
            footer: template.footer || false,
            content: template.content || '',
            footer: template.footer || '',
            styles: template.styles || ''
        });
        console.log(`✓ 面板模板注册: ${templateId}`);
    }

    /**
     * 创建面板
     */
    createPanel(config) {
        const panelId = config.id || `panel_${++this.panelCounter}`;
        
        // 合并配置
        const finalConfig = { ...this.defaultConfig, ...config, id: panelId };
        
        // 获取模板
        const template = this.templates.get(finalConfig.template || 'default-panel');
        if (!template) {
            throw new Error(`PanelManager: 未找到模板 "${finalConfig.template}"`);
        }

        // 创建面板元素
        const panelElement = this._createPanelElement(finalConfig, template);
        
        // 设置面板样式
        this._applyPanelStyles(panelElement, finalConfig);
        
        // 绑定事件
        this._bindPanelEvents(panelElement, finalConfig);
        
        // 添加到DOM
        document.body.appendChild(panelElement);
        
        // 存储面板数据
        const panelData = {
            id: panelId,
            element: panelElement,
            config: finalConfig,
            template: template,
            isVisible: false,
            createTime: Date.now()
        };
        
        this.panels.set(panelId, panelData);
        
        // 触发面板创建事件
        this._triggerEvent('panel:created', { panelId, panelData });
        
        console.log(`✓ 面板创建成功: ${panelId}`);
        return panelId;
    }

    /**
     * 创建面板DOM元素
     */
    _createPanelElement(config, template) {
        const panel = document.createElement('div');
        panel.className = 'analysis-panel panel-managed';
        panel.setAttribute('data-panel-id', config.id);
        panel.style.display = 'none'; // 初始隐藏

        let innerHTML = '';

        // 添加头部
        if (template.header) {
            innerHTML += `
                <div class="panel-header">
                    <div class="panel-title">${config.title || 'Panel'}</div>
                    <div class="panel-controls">
                        ${config.resizable ? '<button class="panel-btn btn-resize" data-action="toggle-resize"><i class="icon-resize"></i></button>' : ''}
                        <button class="panel-btn btn-close" data-action="close"><i class="icon-close"></i>×</button>
                    </div>
                </div>
            `;
        }

        // 添加内容区域
        innerHTML += `<div class="panel-body">${template.content}</div>`;

        // 添加底部
        if (template.footer && template.footer) {
            innerHTML += `<div class="panel-footer">${template.footer}</div>`;
        }

        panel.innerHTML = innerHTML;

        // 添加可拖拽功能
        if (config.draggable) {
            this._makeDraggable(panel);
        }

        // 添加可调整大小功能
        if (config.resizable) {
            this._makeResizable(panel);
        }

        return panel;
    }

    /**
     * 应用面板样式
     */
    _applyPanelStyles(panelElement, config) {
        const styles = {
            width: typeof config.width === 'number' ? `${config.width}px` : config.width,
            height: typeof config.height === 'number' ? `${config.height}px` : config.height,
            zIndex: config.modal ? '10000' : '1000'
        };

        Object.assign(panelElement.style, styles);

        // 添加模态遮罩
        if (config.modal) {
            panelElement.classList.add('panel-modal');
        }
    }

    /**
     * 绑定面板事件
     */
    _bindPanelEvents(panelElement, config) {
        // 事件委托处理所有按钮点击
        panelElement.addEventListener('click', (event) => {
            const action = event.target.getAttribute('data-action');
            if (action) {
                event.preventDefault();
                event.stopPropagation();
                this._handlePanelAction(config.id, action, event);
            }
        });

        // 输入框变化事件
        panelElement.addEventListener('input', (event) => {
            if (event.target.matches('input, textarea, select')) {
                this._handlePanelInput(config.id, event.target, event);
            }
        });

        // 阻止面板内点击事件冒泡（防止意外关闭）
        panelElement.addEventListener('click', (event) => {
            event.stopPropagation();
        });
    }

    /**
     * 处理面板动作
     */
    _handlePanelAction(panelId, action, event) {
        const panelData = this.panels.get(panelId);
        if (!panelData) return;

        // 标准动作处理
        switch (action) {
            case 'close':
                this.closePanel(panelId);
                break;
            case 'minimize':
                this.togglePanel(panelId);
                break;
            case 'maximize':
                this.maximizePanel(panelId);
                break;
            default:
                // 触发自定义动作事件
                this._triggerEvent('panel:action', {
                    panelId,
                    action,
                    target: event.target,
                    event
                });
                break;
        }
    }

    /**
     * 处理面板输入
     */
    _handlePanelInput(panelId, inputElement, event) {
        this._triggerEvent('panel:input', {
            panelId,
            field: inputElement.getAttribute('data-field') || inputElement.name,
            value: inputElement.value,
            element: inputElement,
            event
        });
    }

    /**
     * 显示面板
     */
    showPanel(panelId, anchorElement = null) {
        const panelData = this.panels.get(panelId);
        if (!panelData) {
            throw new Error(`PanelManager: 未找到面板 "${panelId}"`);
        }

        // 关闭当前激活的面板
        if (this.activePanel && this.activePanel !== panelId) {
            this.closePanel(this.activePanel);
        }

        const { element, config } = panelData;

        // 定位面板
        if (anchorElement && window.PanelPositioner) {
            try {
                window.PanelPositioner.setPosition(anchorElement, element, {
                    preferredPosition: config.position,
                    gap: config.gap
                });
            } catch (error) {
                console.warn('面板定位失败，使用默认位置:', error);
                this._setDefaultPosition(element, config);
            }
        } else {
            this._setDefaultPosition(element, config);
        }

        // 显示面板
        if (config.animation) {
            this._showWithAnimation(element);
        } else {
            element.style.display = 'block';
        }

        // 更新状态
        panelData.isVisible = true;
        this.activePanel = panelId;

        // 触发显示事件
        this._triggerEvent('panel:shown', { panelId, panelData });

        console.log(`✓ 面板显示: ${panelId}`);
    }

    /**
     * 关闭面板
     */
    closePanel(panelId) {
        const panelData = this.panels.get(panelId);
        if (!panelData || !panelData.isVisible) return;

        const { element, config } = panelData;

        // 隐藏面板
        if (config.animation) {
            this._hideWithAnimation(element);
        } else {
            element.style.display = 'none';
        }

        // 更新状态
        panelData.isVisible = false;
        if (this.activePanel === panelId) {
            this.activePanel = null;
        }

        // 触发关闭事件
        this._triggerEvent('panel:closed', { panelId, panelData });

        console.log(`✓ 面板关闭: ${panelId}`);
    }

    /**
     * 切换面板显示状态
     */
    togglePanel(panelId, anchorElement = null) {
        const panelData = this.panels.get(panelId);
        if (!panelData) return;

        if (panelData.isVisible) {
            this.closePanel(panelId);
        } else {
            this.showPanel(panelId, anchorElement);
        }
    }

    /**
     * 销毁面板
     */
    destroyPanel(panelId) {
        const panelData = this.panels.get(panelId);
        if (!panelData) return;

        // 关闭面板
        if (panelData.isVisible) {
            this.closePanel(panelId);
        }

        // 移除DOM元素
        if (panelData.element && panelData.element.parentNode) {
            panelData.element.parentNode.removeChild(panelData.element);
        }

        // 清理引用
        this.panels.delete(panelId);

        // 触发销毁事件
        this._triggerEvent('panel:destroyed', { panelId });

        console.log(`✓ 面板销毁: ${panelId}`);
    }

    /**
     * 获取面板数据
     */
    getPanelData(panelId) {
        return this.panels.get(panelId) || null;
    }

    /**
     * 获取所有面板
     */
    getAllPanels() {
        const result = {};
        this.panels.forEach((data, id) => {
            result[id] = {
                id: data.id,
                isVisible: data.isVisible,
                config: data.config,
                createTime: data.createTime
            };
        });
        return result;
    }

    /**
     * 关闭所有面板
     */
    closeAllPanels() {
        this.panels.forEach((_, panelId) => {
            this.closePanel(panelId);
        });
    }

    /**
     * 设置默认位置
     */
    _setDefaultPosition(element, config) {
        element.style.position = 'fixed';
        element.style.right = '80px';
        element.style.top = '50%';
        element.style.transform = 'translateY(-50%)';
    }

    /**
     * 显示动画
     */
    _showWithAnimation(element) {
        element.style.display = 'block';
        element.style.opacity = '0';
        element.style.transform += ' scale(0.9)';
        
        requestAnimationFrame(() => {
            element.style.transition = 'all 0.2s ease-out';
            element.style.opacity = '1';
            element.style.transform = element.style.transform.replace('scale(0.9)', 'scale(1)');
        });
    }

    /**
     * 隐藏动画
     */
    _hideWithAnimation(element) {
        element.style.transition = 'all 0.2s ease-in';
        element.style.opacity = '0';
        element.style.transform += ' scale(0.9)';
        
        setTimeout(() => {
            element.style.display = 'none';
            element.style.transition = '';
        }, 200);
    }

    /**
     * 触发事件
     */
    _triggerEvent(eventName, data) {
        if (this.eventBus && typeof this.eventBus.emit === 'function') {
            this.eventBus.emit(eventName, data);
        }

        // 浏览器自定义事件
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * 使面板可拖拽
     */
    _makeDraggable(panel) {
        const header = panel.querySelector('.panel-header');
        if (!header) return;

        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let panelStart = { x: 0, y: 0 };

        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragStart = { x: e.clientX, y: e.clientY };
            const rect = panel.getBoundingClientRect();
            panelStart = { x: rect.left, y: rect.top };
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });

        function onMouseMove(e) {
            if (!isDragging) return;
            
            const dx = e.clientX - dragStart.x;
            const dy = e.clientY - dragStart.y;
            
            panel.style.left = `${panelStart.x + dx}px`;
            panel.style.top = `${panelStart.y + dy}px`;
            panel.style.right = 'auto';
            panel.style.transform = 'none';
        }

        function onMouseUp() {
            isDragging = false;
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
    }

    /**
     * 使面板可调整大小
     */
    _makeResizable(panel) {
        // 简化的调整大小实现
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'panel-resize-handle';
        resizeHandle.style.cssText = `
            position: absolute;
            bottom: 0;
            right: 0;
            width: 15px;
            height: 15px;
            cursor: se-resize;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><path d="M10,0 L10,10 L0,10 Z" fill="%23ccc"/></svg>') no-repeat;
        `;
        panel.appendChild(resizeHandle);

        let isResizing = false;
        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            const startX = e.clientX;
            const startY = e.clientY;
            const startWidth = parseInt(document.defaultView.getComputedStyle(panel).width, 10);
            const startHeight = parseInt(document.defaultView.getComputedStyle(panel).height, 10);

            function doResize(e) {
                if (!isResizing) return;
                panel.style.width = (startWidth + e.clientX - startX) + 'px';
                panel.style.height = (startHeight + e.clientY - startY) + 'px';
            }

            function stopResize() {
                isResizing = false;
                document.removeEventListener('mousemove', doResize);
                document.removeEventListener('mouseup', stopResize);
            }

            document.addEventListener('mousemove', doResize);
            document.addEventListener('mouseup', stopResize);
        });
    }
}

// 将PanelManager添加到全局作用域
window.PanelManager = PanelManager;