/**
 * 按钮工厂类 - 统一创建和管理工具栏按钮
 * 解决按钮创建代码重复和事件处理不统一的问题
 */
class ButtonFactory {
    constructor() {
        this.buttonIdCounter = 0;
        this.createdButtons = new Map(); // 存储已创建的按钮引用
    }

    /**
     * 创建工具按钮
     * @param {Object} config - 按钮配置
     * @param {string} config.id - 按钮唯一标识
     * @param {string} config.iconPath - 图标路径 (SVG/IMG)
     * @param {string} config.tooltip - 提示文本
     * @param {Function} config.onClick - 点击事件处理函数
     * @param {HTMLElement} config.container - 按钮容器元素
     * @param {Object} [config.options] - 可选配置
     * @param {boolean} [config.options.toggle=true] - 是否为切换按钮
     * @param {string} [config.options.position='append'] - 插入位置: 'append', 'prepend', 'before', 'after'
     * @param {HTMLElement} [config.options.referenceElement] - 参考元素（用于before/after定位）
     * @returns {HTMLElement} 创建的按钮元素
     */
    createButton(config) {
        // 验证必需参数
        this._validateConfig(config);

        const button = this._createButtonElement(config);
        this._setupButtonEvents(button, config);
        this._insertButton(button, config);
        
        // 存储按钮引用
        this.createdButtons.set(config.id, {
            element: button,
            config: config,
            isActive: false
        });

        console.log(`✓ 按钮创建成功: ${config.id}`);
        return button;
    }

    /**
     * 创建按钮元素
     */
    _createButtonElement(config) {
        const button = document.createElement('button');
        button.className = 'tool-button';
        button.setAttribute('data-tool-id', config.id);
        button.title = config.tooltip;

        // 创建按钮内容
        if (config.iconPath.endsWith('.svg')) {
            // SVG图标 - 异步加载
            this._loadSvgIcon(button, config.iconPath, config.tooltip);
        } else {
            // 图片图标
            button.innerHTML = `<img src="${config.iconPath}" alt="${config.tooltip}">`;
        }

        // 添加提示框
        this._createTooltip(button, config.tooltip);

        return button;
    }

    /**
     * 异步加载SVG图标
     */
    _loadSvgIcon(button, iconPath, altText) {
        fetch(iconPath)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(svgContent => {
                button.innerHTML = svgContent;
                // 确保SVG有正确的属性
                const svg = button.querySelector('svg');
                if (svg) {
                    svg.setAttribute('aria-label', altText);
                    svg.style.width = '24px';
                    svg.style.height = '24px';
                }
            })
            .catch(error => {
                console.warn(`SVG图标加载失败 (${iconPath}):`, error);
                // 降级处理 - 使用文本占位符
                button.innerHTML = `<span style="font-size: 12px;">${altText.substring(0, 2)}</span>`;
            });
    }

    /**
     * 创建提示框
     */
    _createTooltip(button, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        button.appendChild(tooltip);
    }

    /**
     * 设置按钮事件
     */
    _setupButtonEvents(button, config) {
        // 使用事件克隆技术避免重复绑定
        const cleanButton = button.cloneNode(true);
        if (button.parentNode) {
            button.parentNode.replaceChild(cleanButton, button);
        }

        // 绑定点击事件
        cleanButton.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();
            
            try {
                const buttonData = this.createdButtons.get(config.id);
                
                // 处理切换按钮逻辑
                if (config.options?.toggle !== false) {
                    this._handleToggleButton(cleanButton, config.id);
                }

                // 执行用户定义的点击处理
                if (typeof config.onClick === 'function') {
                    config.onClick(event, cleanButton, buttonData?.isActive || false);
                }
            } catch (error) {
                console.error(`按钮点击处理错误 (${config.id}):`, error);
            }
        });

        // 更新引用
        if (this.createdButtons.has(config.id)) {
            const buttonData = this.createdButtons.get(config.id);
            buttonData.element = cleanButton;
            this.createdButtons.set(config.id, buttonData);
        }

        return cleanButton;
    }

    /**
     * 处理切换按钮逻辑
     */
    _handleToggleButton(button, buttonId) {
        const buttonData = this.createdButtons.get(buttonId);
        if (!buttonData) return;

        const wasActive = buttonData.isActive;
        
        // 如果按钮当前是激活的，则停用它
        if (wasActive) {
            this._deactivateButton(buttonId);
        } else {
            // 停用其他所有按钮（实现互斥）
            this._deactivateAllButtons();
            // 激活当前按钮
            this._activateButton(buttonId);
        }
    }

    /**
     * 激活按钮
     */
    _activateButton(buttonId) {
        const buttonData = this.createdButtons.get(buttonId);
        if (!buttonData) return;

        buttonData.element.classList.add('active');
        buttonData.isActive = true;
        this.createdButtons.set(buttonId, buttonData);
    }

    /**
     * 停用按钮
     */
    _deactivateButton(buttonId) {
        const buttonData = this.createdButtons.get(buttonId);
        if (!buttonData) return;

        buttonData.element.classList.remove('active');
        buttonData.isActive = false;
        this.createdButtons.set(buttonId, buttonData);
    }

    /**
     * 停用所有按钮
     */
    _deactivateAllButtons() {
        this.createdButtons.forEach((buttonData, buttonId) => {
            this._deactivateButton(buttonId);
        });
    }

    /**
     * 插入按钮到容器
     */
    _insertButton(button, config) {
        const { container, options = {} } = config;
        const position = options.position || 'append';

        switch (position) {
            case 'prepend':
                container.insertBefore(button, container.firstChild);
                break;
            case 'before':
                if (options.referenceElement && options.referenceElement.parentNode) {
                    options.referenceElement.parentNode.insertBefore(button, options.referenceElement);
                } else {
                    container.appendChild(button);
                }
                break;
            case 'after':
                if (options.referenceElement && options.referenceElement.parentNode) {
                    options.referenceElement.parentNode.insertBefore(button, options.referenceElement.nextSibling);
                } else {
                    container.appendChild(button);
                }
                break;
            case 'append':
            default:
                container.appendChild(button);
                break;
        }
    }

    /**
     * 验证配置参数
     */
    _validateConfig(config) {
        const required = ['id', 'iconPath', 'tooltip', 'onClick', 'container'];
        const missing = required.filter(key => !config[key]);
        
        if (missing.length > 0) {
            throw new Error(`ButtonFactory: 缺少必需参数: ${missing.join(', ')}`);
        }

        if (this.createdButtons.has(config.id)) {
            console.warn(`ButtonFactory: 按钮ID "${config.id}" 已存在，将替换现有按钮`);
            this.removeButton(config.id);
        }
    }

    /**
     * 移除按钮
     * @param {string} buttonId - 按钮ID
     */
    removeButton(buttonId) {
        const buttonData = this.createdButtons.get(buttonId);
        if (!buttonData) {
            console.warn(`ButtonFactory: 未找到按钮 "${buttonId}"`);
            return;
        }

        // 移除DOM元素
        if (buttonData.element && buttonData.element.parentNode) {
            buttonData.element.parentNode.removeChild(buttonData.element);
        }

        // 移除引用
        this.createdButtons.delete(buttonId);
        console.log(`✓ 按钮移除成功: ${buttonId}`);
    }

    /**
     * 获取按钮状态
     * @param {string} buttonId - 按钮ID
     * @returns {Object|null} 按钮状态对象
     */
    getButtonState(buttonId) {
        return this.createdButtons.get(buttonId) || null;
    }

    /**
     * 设置按钮状态
     * @param {string} buttonId - 按钮ID
     * @param {boolean} isActive - 是否激活
     */
    setButtonState(buttonId, isActive) {
        if (isActive) {
            this._activateButton(buttonId);
        } else {
            this._deactivateButton(buttonId);
        }
    }

    /**
     * 获取所有按钮状态
     * @returns {Object} 所有按钮的状态映射
     */
    getAllButtonStates() {
        const states = {};
        this.createdButtons.forEach((buttonData, buttonId) => {
            states[buttonId] = {
                isActive: buttonData.isActive,
                tooltip: buttonData.config.tooltip
            };
        });
        return states;
    }

    /**
     * 销毁所有按钮
     */
    destroyAll() {
        this.createdButtons.forEach((_, buttonId) => {
            this.removeButton(buttonId);
        });
        this.createdButtons.clear();
        console.log('✓ 所有按钮已销毁');
    }

    /**
     * 批量创建按钮
     * @param {Array} configs - 按钮配置数组
     * @returns {Array} 创建的按钮元素数组
     */
    createButtons(configs) {
        return configs.map(config => this.createButton(config));
    }
}

// 将ButtonFactory添加到全局作用域
window.ButtonFactory = ButtonFactory;