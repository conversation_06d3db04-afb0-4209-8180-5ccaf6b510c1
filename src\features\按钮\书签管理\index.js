/**
 * 书签管理模块入口
 * 提供书签保存、删除、恢复等功能
 */

// 导入核心组件
import BookmarkTool from './core/BookmarkTool.js';
import BookmarkHandler from './core/BookmarkHandler.js';
import BookmarkUI from './ui/BookmarkUI.js';

// 模块配置
export const BookmarkConfig = {
    name: '书签管理',
    id: 'bookmark',
    tooltip: '保存和管理场景书签',
    icon: './assets/svg/bookmark.svg',
    
    // 存储配置
    storage: {
        key: 'cesiumBookmarks',
        maxCount: 20
    },
    
    // UI配置
    ui: {
        panelWidth: 300,
        panelHeight: 400
    }
};

// 导出组件
export { BookmarkTool, BookmarkHandler, BookmarkUI };

// 默认导出初始化函数
export default {
    /**
     * 初始化书签管理模块
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} containerId - 按钮容器ID
     * @returns {Promise<BookmarkUI>} 书签管理UI实例
     */
    async init(viewer, containerId = 'toolButtons') {
        try {
            const bookmarkUI = await BookmarkUI.init(viewer, containerId);
            return bookmarkUI;
        } catch (error) {
            console.error('书签管理模块初始化失败:', error);
            throw error;
        }
    },
    
    config: BookmarkConfig
};