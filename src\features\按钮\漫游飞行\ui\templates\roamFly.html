<div class="roamfly-container">
    <div class="roamfly-header">
        <h3>路线漫游</h3>
        <button class="close-button" onclick="this.closest('.roamfly-panel').remove()">×</button>
    </div>

    <div class="roamfly-content">
        <!-- 路线绘制 -->
        <div class="control-section">
            <h4>路线设置</h4>
            <div class="button-group">
                <button id="drawRoute" class="btn btn-primary">绘制路线</button>
                <button id="clearRoute" class="btn btn-danger">清除路线</button>
            </div>
        </div>

        <!-- 基本信息 -->
        <div class="info-section">
            <h4>基本信息</h4>
            <div class="info-item">
                <label>路线名称：</label>
                <span id="routeName">-</span>
            </div>
            <div class="info-item">
                <label>总长度：</label>
                <span id="totalLength">0米</span>
            </div>
            <div class="info-item">
                <label>总时间：</label>
                <span id="totalTime">0秒</span>
            </div>
        </div>

        <!-- 视角设置 -->
        <div class="control-section">
            <h4>视角设置</h4>
            <div class="control-item">
                <label for="viewMode">视角模式：</label>
                <select id="viewMode" class="form-control">
                    <option value="normal">普通视角</option>
                    <option value="firstPerson">第一人称</option>
                    <option value="godView">上帝视角</option>
                </select>
            </div>
            <div class="control-item">
                <label for="height">视角高度：</label>
                <input type="range" id="height" class="form-control" 
                       min="0" max="5000" step="10" value="500">
                <span class="value-display">500米</span>
            </div>
        </div>

        <!-- 漫游控制 -->
        <div class="control-section">
            <h4>漫游控制</h4>
            <div class="control-item">
                <label for="speed">漫游速度：</label>
                <input type="range" id="speed" class="form-control"
                       min="10" max="1000" step="10" value="100">
                <span class="value-display">100米/秒</span>
            </div>
            <div class="button-group">
                <button id="startRoam" class="btn btn-primary" disabled>开始漫游</button>
                <button id="stopRoam" class="btn btn-danger" disabled>停止漫游</button>
            </div>
        </div>

        <!-- 实时信息 -->
        <div class="info-section">
            <h4>实时信息</h4>
            <div class="info-item">
                <label>已漫游时间：</label>
                <span id="currentTime">0秒</span>
            </div>
            <div class="info-item">
                <label>已漫游距离：</label>
                <span id="currentLength">0米</span>
            </div>
            <div class="info-item">
                <label>当前位置：</label>
                <span id="currentCoordinates">-</span>
            </div>
            <div class="progress">
                <div id="roamProgress" class="progress-bar" role="progressbar"
                     style="width: 0%" aria-valuenow="0" aria-valuemin="0"
                     aria-valuemax="100"></div>
            </div>
        </div>
    </div>
</div> 