<svg width="1200" height="120" viewBox="0 0 1200 120" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <defs>
    <radialGradient id="techGradient" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#001F3F" stop-opacity="0.9"/>
      <stop offset="85%" stop-color="#000927" stop-opacity="0.95"/>
      <stop offset="100%" stop-color="#000518" stop-opacity="1"/>
    </radialGradient>
    
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#0066CC" stop-opacity="0.7"/>
      <stop offset="50%" stop-color="#00CCFF" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="#0066CC" stop-opacity="0.7"/>
    </linearGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>
  
  <!-- 主背景 -->
  <rect width="100%" height="100%" fill="url(#techGradient)"/>
  
  <!-- 网格线 -->
  <g stroke="#0088FF" stroke-width="0.5" stroke-opacity="0.3">
    <!-- 水平线 -->
    <line x1="0" y1="20" x2="1200" y2="20"/>
    <line x1="0" y1="40" x2="1200" y2="40"/>
    <line x1="0" y1="60" x2="1200" y2="60"/>
    <line x1="0" y1="80" x2="1200" y2="80"/>
    <line x1="0" y1="100" x2="1200" y2="100"/>
    
    <!-- 垂直线 -->
    <line x1="100" y1="0" x2="100" y2="120"/>
    <line x1="200" y1="0" x2="200" y2="120"/>
    <line x1="300" y1="0" x2="300" y2="120"/>
    <line x1="400" y1="0" x2="400" y2="120"/>
    <line x1="500" y1="0" x2="500" y2="120"/>
    <line x1="600" y1="0" x2="600" y2="120"/>
    <line x1="700" y1="0" x2="700" y2="120"/>
    <line x1="800" y1="0" x2="800" y2="120"/>
    <line x1="900" y1="0" x2="900" y2="120"/>
    <line x1="1000" y1="0" x2="1000" y2="120"/>
    <line x1="1100" y1="0" x2="1100" y2="120"/>
  </g>
  
  <!-- 装饰性电路图案 -->
  <g stroke="#00CCFF" stroke-width="1.5" fill="none" stroke-opacity="0.6">
    <path d="M20,20 L100,20 L120,40 L200,40 L230,70 L300,70" filter="url(#glow)"/>
    <path d="M900,20 L1000,20 L1020,40 L1100,40 L1130,70 L1180,70" filter="url(#glow)"/>
    <path d="M20,100 L100,100 L120,80 L200,80 L230,50 L300,50" filter="url(#glow)"/>
    <path d="M900,100 L1000,100 L1020,80 L1100,80 L1130,50 L1180,50" filter="url(#glow)"/>
    
    <!-- 电路节点 -->
    <circle cx="100" cy="20" r="3" fill="#00CCFF" filter="url(#glow)"/>
    <circle cx="200" cy="40" r="3" fill="#00CCFF" filter="url(#glow)"/>
    <circle cx="300" cy="70" r="3" fill="#00CCFF" filter="url(#glow)"/>
    <circle cx="900" cy="20" r="3" fill="#00CCFF" filter="url(#glow)"/>
    <circle cx="1000" cy="20" r="3" fill="#00CCFF" filter="url(#glow)"/>
    <circle cx="1180" cy="70" r="3" fill="#00CCFF" filter="url(#glow)"/>
  </g>
  
  <!-- 雷达扫描效果 -->
  <g transform="translate(150, 60)">
    <circle cx="0" cy="0" r="40" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.2"/>
    <circle cx="0" cy="0" r="30" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.3"/>
    <circle cx="0" cy="0" r="20" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.4"/>
    <circle cx="0" cy="0" r="10" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.5"/>
    
    <!-- 雷达扫描线 -->
    <path d="M0,0 L40,0 A40,40 0 0,0 28.28,-28.28" fill="none" stroke="#00FFFF" stroke-width="2" stroke-opacity="0.7" filter="url(#glow)">
      <animateTransform attributeName="transform" type="rotate" from="0" to="360" dur="3s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 右侧雷达 -->
  <g transform="translate(1050, 60)">
    <circle cx="0" cy="0" r="40" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.2"/>
    <circle cx="0" cy="0" r="30" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.3"/>
    <circle cx="0" cy="0" r="20" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.4"/>
    <circle cx="0" cy="0" r="10" fill="none" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.5"/>
    
    <!-- 雷达扫描线 -->
    <path d="M0,0 L40,0 A40,40 0 0,0 28.28,-28.28" fill="none" stroke="#00FFFF" stroke-width="2" stroke-opacity="0.7" filter="url(#glow)">
      <animateTransform attributeName="transform" type="rotate" from="0" to="360" dur="3s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 中央标题背景框 -->
  <rect x="300" y="15" width="600" height="90" rx="5" ry="5" fill="none" stroke="url(#titleGradient)" stroke-width="2" filter="url(#glow)"/>
  
  <!-- 中央装饰横线 -->
  <line x1="350" y1="25" x2="850" y2="25" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.8" filter="url(#glow)"/>
  <line x1="350" y1="95" x2="850" y2="95" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.8" filter="url(#glow)"/>
  
  <!-- 装饰性瞄准器元素 -->
  <g transform="translate(600, 60)" filter="url(#glow)">
    <circle cx="0" cy="0" r="30" stroke="#00CCFF" stroke-width="1" fill="none" stroke-opacity="0.5"/>
    <line x1="-35" y1="0" x2="-10" y2="0" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.8"/>
    <line x1="10" y1="0" x2="35" y2="0" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.8"/>
    <line x1="0" y1="-35" x2="0" y2="-10" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.8"/>
    <line x1="0" y1="10" x2="0" y2="35" stroke="#00CCFF" stroke-width="1" stroke-opacity="0.8"/>
  </g>
  
  <!-- 左右两侧的军事风格装饰 -->
  <path d="M40,30 L60,30 L70,40 L70,80 L60,90 L40,90 Z" fill="#00264D" stroke="#00CCFF" stroke-width="1" filter="url(#glow)"/>
  <path d="M1160,30 L1140,30 L1130,40 L1130,80 L1140,90 L1160,90 Z" fill="#00264D" stroke="#00CCFF" stroke-width="1" filter="url(#glow)"/>
  
  <!-- 数据标记 -->
  <text x="45" y="40" font-family="Arial" font-size="6" fill="#00CCFF">LAT:37.8°N</text>
  <text x="45" y="50" font-family="Arial" font-size="6" fill="#00CCFF">LON:122.4°W</text>
  <text x="45" y="60" font-family="Arial" font-size="6" fill="#00CCFF">RCS:25dBm²</text>
  
  <text x="1115" y="40" font-family="Arial" font-size="6" fill="#00CCFF" text-anchor="end">ALT:3500m</text>
  <text x="1115" y="50" font-family="Arial" font-size="6" fill="#00CCFF" text-anchor="end">HDG:095°</text>
  <text x="1115" y="60" font-family="Arial" font-size="6" fill="#00CCFF" text-anchor="end">SPD:450km/h</text>
</svg>