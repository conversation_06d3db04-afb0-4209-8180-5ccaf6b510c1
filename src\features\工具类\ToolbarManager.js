/**
 * 工具栏管理器 - 统一管理所有工具按钮和面板
 * 解决按钮状态协调、面板管理和模块间通信问题
 */
class ToolbarManager {
    constructor(viewer, containerId = 'toolButtons') {
        this.viewer = viewer;
        this.containerId = containerId;
        this.container = null;
        
        // 核心管理器
        this.buttonFactory = new ButtonFactory();
        this.panelPositioner = window.PanelPositioner;
        
        // 工具注册表
        this.registeredTools = new Map(); // 工具实例映射
        this.activeTool = null; // 当前激活的工具
        this.activePanels = new Map(); // 活动面板映射
        
        // 事件系统
        this.eventListeners = new Map();
        
        this._initContainer();
        this._setupGlobalEvents();
        
        console.log('✓ ToolbarManager 初始化完成');
    }

    /**
     * 初始化工具栏容器
     */
    _initContainer() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            // 如果容器不存在，创建它
            this.container = document.createElement('div');
            this.container.id = this.containerId;
            this.container.className = 'toolbar-container';
            document.body.appendChild(this.container);
            console.log(`✓ 创建工具栏容器: #${this.containerId}`);
        }
    }

    /**
     * 设置全局事件
     */
    _setupGlobalEvents() {
        // 监听ESC键关闭所有面板
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.deactivateAllTools();
            }
        });

        // 监听点击外部区域关闭面板
        document.addEventListener('click', (event) => {
            // 检查点击是否在工具栏或面板内
            if (!this._isClickInToolbarOrPanel(event.target)) {
                // 延时处理避免与按钮点击冲突
                setTimeout(() => {
                    this._handleOutsideClick();
                }, 100);
            }
        });
    }

    /**
     * 注册工具到工具栏
     * @param {Object} toolConfig - 工具配置
     * @param {string} toolConfig.id - 工具唯一标识
     * @param {string} toolConfig.name - 工具名称
     * @param {string} toolConfig.iconPath - 图标路径
     * @param {string} toolConfig.tooltip - 提示文本
     * @param {Function} toolConfig.createTool - 工具创建函数
     * @param {Function} [toolConfig.createPanel] - 面板创建函数
     * @param {Object} [toolConfig.options] - 可选配置
     * @returns {Promise<Object>} 工具实例和按钮元素
     */
    async registerTool(toolConfig) {
        try {
            this._validateToolConfig(toolConfig);

            // 创建工具实例
            let toolInstance = null;
            if (typeof toolConfig.createTool === 'function') {
                toolInstance = await toolConfig.createTool(this.viewer);
            }

            // 创建按钮
            const button = this.buttonFactory.createButton({
                id: toolConfig.id,
                iconPath: toolConfig.iconPath,
                tooltip: toolConfig.tooltip,
                container: this.container,
                onClick: (event, buttonElement, isActive) => {
                    this._handleToolClick(toolConfig.id, isActive, event);
                },
                options: toolConfig.options
            });

            // 注册工具信息
            this.registeredTools.set(toolConfig.id, {
                config: toolConfig,
                instance: toolInstance,
                button: button,
                panel: null,
                isActive: false
            });

            console.log(`✓ 工具注册成功: ${toolConfig.id}`);
            
            // 触发工具注册事件
            this._triggerEvent('tool:registered', {
                toolId: toolConfig.id,
                toolInstance,
                button
            });

            return { toolInstance, button };
        } catch (error) {
            console.error(`工具注册失败 (${toolConfig.id}):`, error);
            throw error;
        }
    }

    /**
     * 处理工具点击事件
     */
    async _handleToolClick(toolId, wasActive, event) {
        try {
            const toolData = this.registeredTools.get(toolId);
            if (!toolData) return;

            if (wasActive) {
                // 工具已激活，停用它
                await this._deactivateTool(toolId);
            } else {
                // 激活工具
                await this._activateTool(toolId, event);
            }
        } catch (error) {
            console.error(`工具点击处理错误 (${toolId}):`, error);
        }
    }

    /**
     * 激活工具
     */
    async _activateTool(toolId, event) {
        // 先停用当前激活的工具
        if (this.activeTool && this.activeTool !== toolId) {
            await this._deactivateTool(this.activeTool);
        }

        const toolData = this.registeredTools.get(toolId);
        if (!toolData) return;

        try {
            // 创建并显示面板（如果有）
            if (toolData.config.createPanel && !toolData.panel) {
                const panel = await toolData.config.createPanel(this.viewer, toolData.instance);
                if (panel) {
                    toolData.panel = panel;
                    this._setupPanel(toolId, panel, toolData.button);
                }
            }

            // 显示现有面板
            if (toolData.panel) {
                this._showPanel(toolId, toolData.panel, toolData.button);
            }

            // 调用工具的激活方法
            if (toolData.instance && typeof toolData.instance.activate === 'function') {
                toolData.instance.activate();
            }

            // 更新状态
            toolData.isActive = true;
            this.activeTool = toolId;
            this.registeredTools.set(toolId, toolData);

            // 触发工具激活事件
            this._triggerEvent('tool:activated', {
                toolId,
                toolInstance: toolData.instance,
                panel: toolData.panel
            });

            console.log(`✓ 工具激活: ${toolId}`);
        } catch (error) {
            console.error(`工具激活失败 (${toolId}):`, error);
        }
    }

    /**
     * 停用工具
     */
    async _deactivateTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData || !toolData.isActive) return;

        try {
            // 隐藏面板
            if (toolData.panel) {
                this._hidePanel(toolId, toolData.panel);
            }

            // 调用工具的停用方法
            if (toolData.instance && typeof toolData.instance.deactivate === 'function') {
                toolData.instance.deactivate();
            }

            // 更新状态
            toolData.isActive = false;
            this.registeredTools.set(toolId, toolData);

            // 清除激活工具引用
            if (this.activeTool === toolId) {
                this.activeTool = null;
            }

            // 更新按钮状态
            this.buttonFactory.setButtonState(toolId, false);

            // 触发工具停用事件
            this._triggerEvent('tool:deactivated', {
                toolId,
                toolInstance: toolData.instance
            });

            console.log(`✓ 工具停用: ${toolId}`);
        } catch (error) {
            console.error(`工具停用失败 (${toolId}):`, error);
        }
    }

    /**
     * 设置面板
     */
    _setupPanel(toolId, panel, button) {
        // 确保面板有正确的类名
        if (!panel.classList.contains('analysis-panel')) {
            panel.classList.add('analysis-panel');
        }

        // 添加工具标识
        panel.setAttribute('data-tool-id', toolId);

        // 添加关闭按钮事件处理
        const closeButtons = panel.querySelectorAll('[data-action="close"], .close-btn, .panel-close');
        closeButtons.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                this._deactivateTool(toolId);
            });
        });

        // 存储面板引用
        this.activePanels.set(toolId, panel);
    }

    /**
     * 显示面板
     */
    _showPanel(toolId, panel, button) {
        // 使用PanelPositioner定位面板
        if (this.panelPositioner && typeof this.panelPositioner.setPosition === 'function') {
            try {
                this.panelPositioner.setPosition(button, panel, {
                    preferredPosition: 'left',
                    gap: 10
                });
            } catch (error) {
                console.warn('PanelPositioner定位失败，使用默认位置:', error);
                // 降级处理
                panel.style.position = 'fixed';
                panel.style.right = '80px';
                panel.style.top = '50%';
                panel.style.transform = 'translateY(-50%)';
            }
        }

        panel.style.display = 'block';
        
        // 添加显示动画
        panel.style.opacity = '0';
        panel.style.transform += ' scale(0.9)';
        
        requestAnimationFrame(() => {
            panel.style.transition = 'all 0.2s ease-out';
            panel.style.opacity = '1';
            panel.style.transform = panel.style.transform.replace('scale(0.9)', 'scale(1)');
        });
    }

    /**
     * 隐藏面板
     */
    _hidePanel(toolId, panel) {
        panel.style.transition = 'all 0.2s ease-in';
        panel.style.opacity = '0';
        panel.style.transform += ' scale(0.9)';
        
        setTimeout(() => {
            panel.style.display = 'none';
            panel.style.transition = '';
            this.activePanels.delete(toolId);
        }, 200);
    }

    /**
     * 停用所有工具
     */
    async deactivateAllTools() {
        const activeTools = Array.from(this.registeredTools.entries())
            .filter(([_, toolData]) => toolData.isActive)
            .map(([toolId, _]) => toolId);

        for (const toolId of activeTools) {
            await this._deactivateTool(toolId);
        }

        console.log('✓ 所有工具已停用');
    }

    /**
     * 获取工具实例
     */
    getTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        return toolData ? toolData.instance : null;
    }

    /**
     * 获取工具状态
     */
    getToolState(toolId) {
        const toolData = this.registeredTools.get(toolId);
        return toolData ? {
            id: toolId,
            name: toolData.config.name,
            isActive: toolData.isActive,
            hasPanel: !!toolData.panel
        } : null;
    }

    /**
     * 获取所有工具状态
     */
    getAllToolStates() {
        const states = {};
        this.registeredTools.forEach((toolData, toolId) => {
            states[toolId] = {
                name: toolData.config.name,
                isActive: toolData.isActive,
                hasPanel: !!toolData.panel
            };
        });
        return states;
    }

    /**
     * 注销工具
     */
    unregisterTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData) {
            console.warn(`工具未注册: ${toolId}`);
            return;
        }

        // 停用工具
        if (toolData.isActive) {
            this._deactivateTool(toolId);
        }

        // 移除面板
        if (toolData.panel && toolData.panel.parentNode) {
            toolData.panel.parentNode.removeChild(toolData.panel);
        }

        // 销毁工具实例
        if (toolData.instance && typeof toolData.instance.destroy === 'function') {
            toolData.instance.destroy();
        }

        // 移除按钮
        this.buttonFactory.removeButton(toolId);

        // 移除注册
        this.registeredTools.delete(toolId);

        console.log(`✓ 工具注销成功: ${toolId}`);
    }

    /**
     * 事件系统
     */
    on(eventName, callback) {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, new Set());
        }
        this.eventListeners.get(eventName).add(callback);
    }

    off(eventName, callback) {
        if (this.eventListeners.has(eventName)) {
            this.eventListeners.get(eventName).delete(callback);
        }
    }

    _triggerEvent(eventName, data) {
        if (this.eventListeners.has(eventName)) {
            this.eventListeners.get(eventName).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理错误 (${eventName}):`, error);
                }
            });
        }
    }

    /**
     * 验证工具配置
     */
    _validateToolConfig(toolConfig) {
        const required = ['id', 'name', 'iconPath', 'tooltip'];
        const missing = required.filter(key => !toolConfig[key]);
        
        if (missing.length > 0) {
            throw new Error(`工具配置缺少必需参数: ${missing.join(', ')}`);
        }

        if (this.registeredTools.has(toolConfig.id)) {
            console.warn(`工具ID "${toolConfig.id}" 已存在，将替换现有工具`);
            this.unregisterTool(toolConfig.id);
        }
    }

    /**
     * 检查点击是否在工具栏或面板内
     */
    _isClickInToolbarOrPanel(target) {
        // 检查是否在工具栏内
        if (this.container.contains(target)) {
            return true;
        }

        // 检查是否在任何激活的面板内
        for (const panel of this.activePanels.values()) {
            if (panel.contains(target)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理外部点击
     */
    _handleOutsideClick() {
        // 可以在这里实现自定义的外部点击逻辑
        // 默认不执行任何操作，让工具自己决定是否需要关闭
    }

    /**
     * 销毁管理器
     */
    destroy() {
        // 停用所有工具
        this.deactivateAllTools();

        // 销毁所有工具实例
        this.registeredTools.forEach((toolData, toolId) => {
            this.unregisterTool(toolId);
        });

        // 销毁按钮工厂
        this.buttonFactory.destroyAll();

        // 清理事件监听器
        this.eventListeners.clear();

        // 移除容器
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        console.log('✓ ToolbarManager 已销毁');
    }
}

// 将ToolbarManager添加到全局作用域
window.ToolbarManager = ToolbarManager;