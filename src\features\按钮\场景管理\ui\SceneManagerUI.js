/**
 * 场景管理器UI组件
 * 提供场景管理界面
 */
import { SceneManagerConfig } from '../config.js';
import { SceneManagerTool } from '../core/SceneManagerTool.js';

export class SceneManagerUI {
    /**
     * 创建场景管理UI组件
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.tool = null;
        this.panelId = 'sceneManagerPanel';
        this.toggleBtnId = 'toggleSceneManager';
        this.dialogId = 'saveSceneDialog';
        this.backdropId = 'sceneManagerBackdrop';
        this.messageId = 'sceneManagerMessage';
        
        // 当前场景ID
        this.currentSceneId = null;
    }

    /**
     * 生成UI组件的HTML代码
     * @returns {string} HTML代码字符串
     */
    generateHTML() {
        return `
        <!-- 场景管理按钮 -->
        <button id="${this.toggleBtnId}">
            <img src="${SceneManagerConfig.ui.iconPath}" alt="${SceneManagerConfig.ui.tooltipText}">
            <div class="tooltip">${SceneManagerConfig.ui.tooltipText}</div>
        </button>
        
        <!-- 场景管理面板 -->
        <div id="${this.panelId}" class="scene-manager-panel">
            <div class="scene-manager-title">
                ${SceneManagerConfig.defaultTitle}
                <span id="scenesCount">(0)</span>
            </div>
            <div id="${this.messageId}" class="scene-manager-message" style="display:none;"></div>
            <div class="scene-list-container" id="sceneListContainer">
                <div class="no-scenes-message" id="noScenesMessage">
                    暂无保存的场景
                </div>
            </div>
            <div class="scene-manager-buttons">
                <button class="btn-scene-manager btn-save-scene" id="btnSaveScene">保存当前场景</button>
                <button class="btn-scene-manager btn-close-panel" id="btnClosePanel">关闭</button>
            </div>
        </div>
        
        <!-- 保存场景对话框 -->
        <div id="${this.dialogId}" class="save-scene-dialog">
            <div class="save-scene-dialog-title">保存场景</div>
            <input type="text" id="sceneNameInput" placeholder="输入场景名称">
            <div class="save-scene-dialog-buttons">
                <button class="btn-scene-manager btn-cancel" id="btnCancelSave">取消</button>
                <button class="btn-scene-manager btn-confirm" id="btnConfirmSave">保存</button>
            </div>
        </div>
        
        <!-- 背景遮罩 -->
        <div id="${this.backdropId}" class="scene-manager-backdrop"></div>`;
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <button id="${this.toggleBtnId}">
            <img src="${SceneManagerConfig.ui.iconPath}" alt="${SceneManagerConfig.ui.tooltipText}">
            <div class="tooltip">${SceneManagerConfig.ui.tooltipText}</div>
        </button>`;
        
        container.appendChild(tempDiv.firstElementChild);
        console.log(`场景管理按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 场景管理面板 -->
        <div id="${this.panelId}" class="scene-manager-panel">
            <div class="scene-manager-title">
                ${SceneManagerConfig.defaultTitle}
                <span id="scenesCount">(0)</span>
            </div>
            <div id="${this.messageId}" class="scene-manager-message" style="display:none;"></div>
            <div class="scene-list-container" id="sceneListContainer">
                <div class="no-scenes-message" id="noScenesMessage">
                    暂无保存的场景
                </div>
            </div>
            <div class="scene-manager-buttons">
                <button class="btn-scene-manager btn-save-scene" id="btnSaveScene">保存当前场景</button>
                <button class="btn-scene-manager btn-close-panel" id="btnClosePanel">关闭</button>
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        
        // 添加对话框和背景遮罩
        tempDiv.innerHTML = `
        <!-- 保存场景对话框 -->
        <div id="${this.dialogId}" class="save-scene-dialog">
            <div class="save-scene-dialog-title">保存场景</div>
            <input type="text" id="sceneNameInput" placeholder="输入场景名称">
            <div class="save-scene-dialog-buttons">
                <button class="btn-scene-manager btn-cancel" id="btnCancelSave">取消</button>
                <button class="btn-scene-manager btn-confirm" id="btnConfirmSave">保存</button>
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        
        tempDiv.innerHTML = `<!-- 背景遮罩 --><div id="${this.backdropId}" class="scene-manager-backdrop"></div>`;
        document.body.appendChild(tempDiv.firstElementChild);
        
        console.log('场景管理面板已添加到页面');
    }

    /**
     * 初始化场景管理组件及事件绑定
     */
    init() {
        console.log('正在初始化场景管理UI组件...');
        
        // 创建工具实例
        this.tool = new SceneManagerTool(this.viewer);
        
        // 为场景管理器面板添加样式
        const style = document.createElement('style');
        style.textContent = `
            .scene-manager-message {
                margin: 10px 0;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 14px;
                display: none;
            }
            
            .message-success {
                background-color: #e8f5e9;
                color: #2e7d32;
                border-left: 4px solid #4caf50;
            }
            
            .message-error {
                background-color: #ffebee;
                color: #c62828;
                border-left: 4px solid #f44336;
            }
            
            .message-info {
                background-color: #e3f2fd;
                color: #1565c0;
                border-left: 4px solid #2196f3;
            }
        `;
        document.head.appendChild(style);
        
        // 切换面板显示/隐藏的全局函数
        window.toggleSceneManagerPanel = () => {
            console.log('调用toggleSceneManagerPanel函数');
            const panel = document.getElementById(this.panelId);
            if (panel) {
                const currentDisplay = window.getComputedStyle(panel).display;
                
                if (currentDisplay === 'none') {
                    panel.style.display = 'block';
                    
                    // 隐藏其他面板
                    ['terrainDigPanel', 'searchPanel', 'measureToolContainer', 'profileAnalysisPanel'].forEach(id => {
                        const otherPanel = document.getElementById(id);
                        if (otherPanel) otherPanel.style.display = 'none';
                    });
                    
                    // 设置面板位置
                    this.positionPanel();
                    
                    // 更新场景列表
                    this.updateSceneList();
                } else {
                    panel.style.display = 'none';
                }
            } else {
                console.error('场景管理面板元素不存在');
            }
        };
        
        // 绑定事件
        this.bindEvents();
        
        console.log('场景管理UI组件初始化完成');
    }

    /**
     * 设置面板位置
     */
    positionPanel() {
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (panel && toggleBtn && window.PanelPositioner) {
            try {
                window.PanelPositioner.setPosition(toggleBtn, panel, {
                    preferredPosition: 'right',
                    gap: 10
                });
            } catch (e) {
                console.error('面板定位出错:', e);
                // 默认位置
                panel.style.position = 'absolute';
                panel.style.top = '100px';
                panel.style.left = '100px';
            }
        } else {
            console.error('无法设置面板位置:', {
                'panel存在': !!panel,
                'toggleBtn存在': !!toggleBtn,
                'PanelPositioner存在': !!window.PanelPositioner
            });
        }
    }

    /**
     * 绑定事件处理函数
     */
    bindEvents() {
        // 绑定切换按钮事件
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            // 清除可能存在的事件绑定
            const newToggleBtn = toggleBtn.cloneNode(true);
            if (toggleBtn.parentNode) {
                toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
            }
            
            newToggleBtn.addEventListener('click', () => {
                window.toggleSceneManagerPanel();
            });
        }
        
        // 绑定保存场景按钮事件
        const btnSaveScene = document.getElementById('btnSaveScene');
        if (btnSaveScene) {
            btnSaveScene.addEventListener('click', () => {
                this.showSaveDialog();
            });
        }
        
        // 绑定关闭面板按钮事件
        const btnClosePanel = document.getElementById('btnClosePanel');
        if (btnClosePanel) {
            btnClosePanel.addEventListener('click', () => {
                const panel = document.getElementById(this.panelId);
                if (panel) panel.style.display = 'none';
            });
        }
        
        // 绑定保存场景对话框的确认和取消按钮
        const btnConfirmSave = document.getElementById('btnConfirmSave');
        if (btnConfirmSave) {
            btnConfirmSave.addEventListener('click', () => {
                this.saveScene();
            });
        }
        
        const btnCancelSave = document.getElementById('btnCancelSave');
        if (btnCancelSave) {
            btnCancelSave.addEventListener('click', () => {
                this.hideSaveDialog();
            });
        }
        
        // 场景名称输入框回车事件
        const sceneNameInput = document.getElementById('sceneNameInput');
        if (sceneNameInput) {
            sceneNameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.saveScene();
                }
            });
        }
        
        // 背景遮罩点击事件
        const backdrop = document.getElementById(this.backdropId);
        if (backdrop) {
            backdrop.addEventListener('click', () => {
                this.hideSaveDialog();
            });
        }
    }
    
    /**
     * 显示保存场景对话框
     */
    showSaveDialog() {
        const dialog = document.getElementById(this.dialogId);
        const backdrop = document.getElementById(this.backdropId);
        const input = document.getElementById('sceneNameInput');
        
        if (dialog && backdrop && input) {
            // 显示对话框和背景遮罩
            dialog.style.display = 'block';
            backdrop.style.display = 'block';
            
            // 设置默认场景名称(添加时间标记)
            const now = new Date();
            const timeStr = `${now.getMonth()+1}月${now.getDate()}日 ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
            input.value = `${SceneManagerConfig.defaultSceneName} - ${timeStr}`;
            
            // 聚焦输入框
            setTimeout(() => {
                input.focus();
                input.select();
            }, 100);
        }
    }
    
    /**
     * 隐藏保存场景对话框
     */
    hideSaveDialog() {
        const dialog = document.getElementById(this.dialogId);
        const backdrop = document.getElementById(this.backdropId);
        
        if (dialog && backdrop) {
            dialog.style.display = 'none';
            backdrop.style.display = 'none';
        }
    }
    
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型: 'success', 'error', 'info'
     * @param {number} duration - 显示时长，毫秒，默认3000
     */
    showMessage(message, type = 'info', duration = 3000) {
        const messageEl = document.getElementById(this.messageId);
        if (!messageEl) return;
        
        // 设置消息类型样式
        messageEl.className = 'scene-manager-message';
        messageEl.classList.add(`message-${type}`);
        
        // 设置消息内容
        messageEl.textContent = message;
        
        // 显示消息
        messageEl.style.display = 'block';
        
        // 定时隐藏消息
        if (this._messageTimeout) {
            clearTimeout(this._messageTimeout);
        }
        
        this._messageTimeout = setTimeout(() => {
            messageEl.style.display = 'none';
        }, duration);
    }
    
    /**
     * 保存当前场景
     */
    saveScene() {
        const input = document.getElementById('sceneNameInput');
        if (!input || !this.tool) {
            this.showMessage('保存场景失败：缺少必要元素', 'error');
            return;
        }
        
        const sceneName = input.value.trim();
        
        if (sceneName) {
            try {
                // 保存场景
                const sceneData = this.tool.saveCurrentScene(sceneName);
                
                if (sceneData && sceneData.id) {
                    // 更新当前场景ID
                    this.currentSceneId = sceneData.id;
                    
                    // 显示成功消息
                    this.showMessage(`已保存场景: ${sceneName}`, 'success');
                    
                    // 隐藏对话框
                    this.hideSaveDialog();
                    
                    // 更新场景列表
                    this.updateSceneList();
                } else {
                    this.showMessage('保存场景失败', 'error');
                }
            } catch (error) {
                console.error('保存场景时出错:', error);
                this.showMessage('保存场景时出错，请查看控制台', 'error');
            }
        } else {
            this.showMessage('请输入场景名称', 'error');
            input.focus();
        }
    }
    
    /**
     * 更新场景列表显示
     */
    updateSceneList() {
        const container = document.getElementById('sceneListContainer');
        const noScenesMessage = document.getElementById('noScenesMessage');
        const scenesCountElem = document.getElementById('scenesCount');
        
        if (!container || !noScenesMessage || !this.tool) {
            console.error('更新场景列表失败：UI元素或工具实例不存在');
            return;
        }
        
        try {
            const scenes = this.tool.getScenes();
            
            // 更新场景计数
            if (scenesCountElem) {
                scenesCountElem.textContent = `(${scenes.length})`;
            }
            
            // 清空现有内容，确保没有残留项
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }
            
            if (scenes.length === 0) {
                // 显示无场景消息
                container.appendChild(noScenesMessage);
                console.log('场景列表为空，显示无场景消息');
            } else {
                // 隐藏无场景消息
                if (noScenesMessage.parentNode === container) {
                    container.removeChild(noScenesMessage);
                }
                
                console.log(`正在添加${scenes.length}个场景项到列表`);
                
                // 添加场景项，使用DocumentFragment提高性能
                const fragment = document.createDocumentFragment();
                
                // 按照时间戳排序，最新的场景排在前面
                const sortedScenes = [...scenes].sort((a, b) => {
                    return new Date(b.timestamp) - new Date(a.timestamp);
                });
                
                sortedScenes.forEach(scene => {
                    // 仅当场景有效时添加
                    if (!scene || !scene.id) return;
                    
                    const sceneItem = document.createElement('div');
                    sceneItem.className = 'scene-item';
                    sceneItem.setAttribute('data-id', scene.id);
                    
                    // 创建场景名称元素
                    const sceneName = document.createElement('div');
                    sceneName.className = 'scene-name';
                    sceneName.textContent = scene.name;
                    
                    // 为场景名称添加提示信息
                    if (scene.timestamp) {
                        const date = new Date(scene.timestamp);
                        const formattedDate = `${date.getFullYear()}/${date.getMonth()+1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
                        sceneName.title = `创建时间: ${formattedDate}`;
                    }
                    
                    // 创建场景操作按钮组
                    const buttonGroup = document.createElement('div');
                    buttonGroup.className = 'scene-item-buttons';
                    
                    // 加载按钮
                    const loadBtn = document.createElement('button');
                    loadBtn.className = 'scene-item-button scene-load-button';
                    loadBtn.innerHTML = '<i class="fas fa-play"></i>';
                    loadBtn.title = '加载场景';
                    loadBtn.addEventListener('click', (e) => {
                        e.stopPropagation(); // 防止事件冒泡
                        this.loadScene(scene.id);
                    });
                    
                    // 重命名按钮
                    const renameBtn = document.createElement('button');
                    renameBtn.className = 'scene-item-button scene-rename-button';
                    renameBtn.innerHTML = '<i class="fas fa-edit"></i>';
                    renameBtn.title = '重命名';
                    renameBtn.addEventListener('click', (e) => {
                        e.stopPropagation(); // 防止事件冒泡
                        this.renameScene(scene.id, scene.name);
                    });
                    
                    // 删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'scene-item-button scene-delete-button';
                    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                    deleteBtn.title = '删除';
                    deleteBtn.addEventListener('click', (e) => {
                        e.stopPropagation(); // 防止事件冒泡
                        this.deleteScene(scene.id);
                    });
                    
                    // 添加按钮到按钮组
                    buttonGroup.appendChild(loadBtn);
                    buttonGroup.appendChild(renameBtn);
                    buttonGroup.appendChild(deleteBtn);
                    
                    // 组装场景项
                    sceneItem.appendChild(sceneName);
                    sceneItem.appendChild(buttonGroup);
                    
                    // 点击场景项也可以加载场景
                    sceneItem.addEventListener('click', () => {
                        this.loadScene(scene.id);
                    });
                    
                    // 添加到fragment
                    fragment.appendChild(sceneItem);
                });
                
                // 一次性添加所有场景项
                container.appendChild(fragment);
            }
            
            console.log('场景列表更新完成');
        } catch (error) {
            console.error('更新场景列表时出错:', error);
        }
    }
    
    /**
     * 加载场景
     * @param {string} sceneId - 场景ID
     */
    loadScene(sceneId) {
        if (!this.tool) {
            this.showMessage('场景管理工具未初始化', 'error');
            return;
        }
        
        if (!sceneId) {
            this.showMessage('场景ID无效', 'error');
            return;
        }
        
        // 验证场景是否存在
        const scene = this.tool.getScene(sceneId);
        if (!scene) {
            this.showMessage('该场景不存在或已被删除', 'error');
            // 更新场景列表，确保显示正确的场景
            this.updateSceneList();
            return;
        }
        
        try {
            const success = this.tool.loadScene(sceneId);
            
            if (success) {
                // 更新当前场景ID
                this.currentSceneId = sceneId;
                
                // 显示成功消息
                this.showMessage(`已加载场景: ${scene.name}`, 'success');
                
                // 更新UI，标记当前场景为活动状态
                const sceneItems = document.querySelectorAll('.scene-item');
                sceneItems.forEach(item => {
                    if (item.getAttribute('data-id') === sceneId) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            } else {
                this.showMessage(`加载场景失败: ${scene.name}`, 'error');
            }
        } catch (error) {
            console.error('加载场景时出错:', error);
            this.showMessage('加载场景时出错，请查看控制台', 'error');
        }
    }
    
    /**
     * 删除场景
     * @param {string} sceneId - 场景ID
     */
    deleteScene(sceneId) {
        if (!this.tool) {
            this.showMessage('场景管理工具未初始化', 'error');
            return;
        }
        
        if (!sceneId) {
            this.showMessage('场景ID无效', 'error');
            return;
        }
        
        // 验证场景是否存在
        const scene = this.tool.getScene(sceneId);
        if (!scene) {
            this.showMessage('该场景不存在或已被删除', 'error');
            // 更新场景列表，确保显示正确的场景
            this.updateSceneList();
            return;
        }
        
        if (confirm(`确定要删除场景 "${scene.name}" 吗？`)) {
            try {
                const success = this.tool.deleteScene(sceneId);
                
                if (success) {
                    // 显示成功消息
                    this.showMessage(`已删除场景: ${scene.name}`, 'success');
                    
                    // 如果删除的是当前选中的场景，清除当前场景ID
                    if (this.currentSceneId === sceneId) {
                        this.currentSceneId = null;
                    }
                    
                    // 立即从DOM中移除对应元素，避免等待updateSceneList完成
                    const sceneItem = document.querySelector(`.scene-item[data-id="${sceneId}"]`);
                    if (sceneItem && sceneItem.parentNode) {
                        sceneItem.parentNode.removeChild(sceneItem);
                    }
                    
                    // 更新场景计数
                    const scenesCountElem = document.getElementById('scenesCount');
                    if (scenesCountElem) {
                        const scenes = this.tool.getScenes();
                        scenesCountElem.textContent = `(${scenes.length})`;
                    }
                    
                    // 如果没有场景了，显示无场景消息
                    const scenes = this.tool.getScenes();
                    if (scenes.length === 0) {
                        const container = document.getElementById('sceneListContainer');
                        const noScenesMessage = document.getElementById('noScenesMessage');
                        if (container && noScenesMessage) {
                            container.appendChild(noScenesMessage);
                        }
                    }
                } else {
                    this.showMessage('删除场景失败', 'error');
                    // 完整更新场景列表，以确保UI一致性
                    this.updateSceneList();
                }
            } catch (error) {
                console.error('删除场景时出错:', error);
                this.showMessage('删除场景时出错，请查看控制台', 'error');
                // 出错时也更新场景列表
                this.updateSceneList();
            }
        }
    }
    
    /**
     * 重命名场景
     * @param {string} sceneId - 场景ID
     * @param {string} currentName - 当前名称
     */
    renameScene(sceneId, currentName) {
        if (!this.tool) {
            console.error('场景管理工具未初始化');
            return;
        }
        
        if (!sceneId) {
            console.error('场景ID无效');
            return;
        }
        
        // 验证场景是否存在
        const scene = this.tool.getScene(sceneId);
        if (!scene) {
            console.warn(`ID为${sceneId}的场景不存在，可能已被删除`);
            // 更新场景列表，确保显示正确的场景
            this.updateSceneList();
            return;
        }
        
        const newName = prompt('请输入新的场景名称', currentName || '');
        
        if (newName && newName.trim() !== '') {
            try {
                const success = this.tool.renameScene(sceneId, newName.trim());
                
                if (success) {
                    console.log(`成功重命名场景为: ${newName}`);
                    
                    // 直接更新DOM元素，无需重新渲染整个列表
                    const sceneItem = document.querySelector(`.scene-item[data-id="${sceneId}"] .scene-name`);
                    if (sceneItem) {
                        sceneItem.textContent = newName.trim();
                    } else {
                        // 如果找不到元素，则更新整个列表
                        this.updateSceneList();
                    }
                } else {
                    console.error(`重命名场景失败: ${scene.name}`);
                    // 更新整个场景列表
                    this.updateSceneList();
                }
            } catch (error) {
                console.error('重命名场景时出错:', error);
                // 出错时也更新场景列表
                this.updateSceneList();
            }
        }
    }
    
    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {SceneManagerUI} 场景管理UI实例
     */
    static init(viewer, toolButtonsId = 'toolButtons') {
        if (!viewer) {
            console.error('初始化场景管理UI组件失败: Viewer不能为空');
            return null;
        }
        
        const ui = new SceneManagerUI(viewer);
        
        // 添加按钮到工具栏
        ui.appendButtonTo(toolButtonsId);
        
        // 添加面板到页面
        ui.appendPanelToBody();
        
        // 初始化组件
        ui.init();
        
        console.log('场景管理UI组件初始化完成');
        return ui;
    }
} 